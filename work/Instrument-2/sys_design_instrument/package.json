{"name": "realtime-interview-assistant", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@assistant-ui/react": "^0.10.37", "@assistant-ui/react-ai-sdk": "^0.11.3", "@assistant-ui/react-markdown": "^0.10.8", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "ai": "^5.0.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.539.0", "next": "15.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.6", "ws": "^8.18.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "@types/ws": "^8.5.12", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "typescript": "^5"}}