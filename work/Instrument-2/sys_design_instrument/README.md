# Realtime Interview Assistant

A speech-to-speech assistant for mock system design interviews using OpenAI's Realtime API with assistant-ui integration.

## Features

- **Real-time Speech-to-Speech**: Direct WebSocket connection to OpenAI Realtime API
- **Manual Trigger Control**: Disable server-side VAD, use spacebar to trigger AI responses
- **Audio Controls**: Adjustable playback speed, mute/unmute functionality
- **Live Transcription**: Real-time display of conversation transcripts
- **Interview-Specific Prompts**: Customized system prompt for system design interviews
- **Session Management**: Auto-reconnection with conversation history preservation

## Setup Instructions

### 1. Environment Setup

1. Copy the environment template:
   ```bash
   cp .env.local.example .env.local
   ```

2. Add your OpenAI API key to `.env.local`:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   ```

### 2. Install Dependencies

```bash
npm install
```

### 3. Run Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

### Basic Operation

1. **Connect**: Click the "Connect" button to establish WebSocket connection
2. **Start Recording**: The app automatically starts listening to your microphone
3. **Trigger AI Response**: Press **Spacebar** or click "Ask AI" to get AI feedback
4. **Audio Controls**: 
   - Press **M** to mute/unmute audio output
   - Use the speed slider to adjust playback rate (0.5x to 2x)

### Interview Workflow

1. **Setup**: Connect to the service and ensure microphone permissions
2. **Conversation**: Have your mock interview conversation normally
3. **AI Feedback**: When you want AI input, say something like "AI, your turn" and press spacebar
4. **Continue**: The AI will provide system design feedback, then continue your conversation

## Architecture

### Core Components

- **RealtimeWebSocket**: Manages WebSocket connection to OpenAI Realtime API
- **AudioPipeline**: Handles microphone input and audio output with Web Audio API
- **RealtimeRuntimeAdapter**: Bridges realtime conversation with assistant-ui
- **SimpleThread**: Custom chat interface for displaying conversation transcripts

### Key Features

- **Server-side VAD Disabled**: Manual control prevents premature AI responses
- **Continuous Audio Buffering**: Server-side buffering for efficient audio handling
- **Session Timeout Management**: Auto-reconnection at 25-minute mark
- **Real-time Transcription**: Whisper-powered transcription of all audio

## System Design Prompt

The AI is configured with a specialized prompt for system design interviews:

- Only responds when explicitly triggered
- Focuses on scalability, trade-offs, and architectural decisions
- Provides clarifying questions and probes deeper thinking
- Maintains interview-appropriate tone and pacing

## Technical Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **UI**: Tailwind CSS, assistant-ui components
- **Audio**: Web Audio API, MediaRecorder API
- **Real-time**: WebSocket connection to OpenAI Realtime API
- **AI**: OpenAI GPT-4o Realtime model with Whisper transcription

## Development

### Build

```bash
npm run build
```

### Lint

```bash
npm run lint
```

## Troubleshooting

### Common Issues

1. **Microphone Permission**: Ensure browser has microphone access
2. **WebSocket Connection**: Check OpenAI API key and network connectivity
3. **Audio Issues**: Verify browser audio permissions and output device

### Browser Compatibility

- Chrome/Chromium: Full support
- Firefox: Full support
- Safari: Limited Web Audio API support

## Next Steps

- [ ] Implement manual trigger system (spacebar)
- [ ] Add audio controls (speed, mute)
- [ ] Implement session management with reconnection
- [ ] Add interview-specific customizations
- [ ] Enhance error handling and recovery
- [ ] Add conversation export functionality

## License

MIT License
