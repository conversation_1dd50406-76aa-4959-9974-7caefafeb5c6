"use client";

import { Mic, MicOff } from "lucide-react";

interface TriggerButtonProps {
  onTrigger: () => void;
  disabled: boolean;
  isRecording: boolean;
}

export function TriggerButton({
  onTrigger,
  disabled,
  isRecording,
}: TriggerButtonProps) {
  return (
    <div className="flex items-center gap-3">
      {/* Recording status */}
      <div className="flex items-center gap-2">
        {isRecording ? (
          <Mic className="w-4 h-4 text-green-600" />
        ) : (
          <MicOff className="w-4 h-4 text-gray-400" />
        )}
        <span className="text-sm text-gray-600">
          {isRecording ? "Listening..." : "Not recording"}
        </span>
      </div>

      {/* Trigger button */}
      <button
        onClick={onTrigger}
        disabled={disabled}
        className={`px-6 py-3 rounded-lg font-medium transition-all ${
          disabled
            ? "bg-gray-200 text-gray-400 cursor-not-allowed"
            : "bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 shadow-md hover:shadow-lg"
        }`}
        title="Trigger AI Response (Spacebar)"
      >
        🎤 Ask AI
      </button>
    </div>
  );
}
