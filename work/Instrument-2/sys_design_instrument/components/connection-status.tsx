"use client";

interface ConnectionStatusProps {
  isConnected: boolean;
  isConnecting: boolean;
  onConnect: () => void;
  onDisconnect: () => void;
}

export function ConnectionStatus({
  isConnected,
  isConnecting,
  onConnect,
  onDisconnect,
}: ConnectionStatusProps) {
  return (
    <div className="flex items-center gap-3">
      {/* Status indicator */}
      <div className="flex items-center gap-2">
        <div
          className={`w-3 h-3 rounded-full ${
            isConnected
              ? "bg-green-500"
              : isConnecting
              ? "bg-yellow-500 animate-pulse"
              : "bg-red-500"
          }`}
        />
        <span className="text-sm font-medium">
          {isConnected
            ? "Connected"
            : isConnecting
            ? "Connecting..."
            : "Disconnected"}
        </span>
      </div>

      {/* Connect/Disconnect button */}
      <button
        onClick={isConnected ? onDisconnect : onConnect}
        disabled={isConnecting}
        className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
          isConnected
            ? "bg-red-100 text-red-700 hover:bg-red-200"
            : "bg-blue-100 text-blue-700 hover:bg-blue-200"
        } disabled:opacity-50 disabled:cursor-not-allowed`}
      >
        {isConnecting ? "Connecting..." : isConnected ? "Disconnect" : "Connect"}
      </button>
    </div>
  );
}
