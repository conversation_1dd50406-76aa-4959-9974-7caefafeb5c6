"use client";

import { Volume2, VolumeX } from "lucide-react";

interface AudioControlsProps {
  isMuted: boolean;
  playbackRate: number;
  onMuteToggle: () => void;
  onPlaybackRateChange: (rate: number) => void;
}

export function AudioControls({
  isMuted,
  playbackRate,
  onMuteToggle,
  onPlaybackRateChange,
}: AudioControlsProps) {
  const playbackRates = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

  return (
    <div className="flex items-center gap-4">
      {/* Mute/Unmute button */}
      <button
        onClick={onMuteToggle}
        className={`p-2 rounded-md transition-colors ${
          isMuted
            ? "bg-red-100 text-red-700 hover:bg-red-200"
            : "bg-gray-100 text-gray-700 hover:bg-gray-200"
        }`}
        title={isMuted ? "Unmute (M)" : "Mute (M)"}
      >
        {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
      </button>

      {/* Playback speed control */}
      <div className="flex items-center gap-2">
        <label className="text-sm font-medium text-gray-700">Speed:</label>
        <select
          value={playbackRate}
          onChange={(e) => onPlaybackRateChange(parseFloat(e.target.value))}
          className="px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {playbackRates.map((rate) => (
            <option key={rate} value={rate}>
              {rate}x
            </option>
          ))}
        </select>
      </div>

      {/* Audio status indicator */}
      <div className="text-xs text-gray-500">
        {isMuted ? "Audio muted" : `Playing at ${playbackRate}x speed`}
      </div>
    </div>
  );
}
