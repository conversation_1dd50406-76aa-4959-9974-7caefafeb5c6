"use client";

import { useEffect, useState } from "react";
import { RealtimeRuntimeAdapter, RealtimeMessage } from "@/lib/realtime-runtime-adapter";

interface SimpleThreadProps {
  runtime: RealtimeRuntimeAdapter;
}

export function SimpleThread({ runtime }: SimpleThreadProps) {
  const [messages, setMessages] = useState<RealtimeMessage[]>([]);

  useEffect(() => {
    const updateMessages = () => {
      setMessages([...runtime.messages]);
    };

    // Subscribe to runtime changes
    const unsubscribe = runtime.subscribe(updateMessages);
    
    // Initial load
    updateMessages();

    return unsubscribe;
  }, [runtime]);

  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <div className="text-lg font-medium mb-2">Ready to start</div>
          <div className="text-sm">
            Connect and start speaking. Press spacebar to trigger AI responses.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto p-6">
      <div className="space-y-4 max-w-4xl mx-auto">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${
                message.role === "user"
                  ? "bg-blue-600 text-white"
                  : "bg-white border border-gray-200 shadow-sm"
              }`}
            >
              <div className="text-sm leading-relaxed whitespace-pre-wrap">
                {message.content}
              </div>
              <div className={`text-xs mt-2 ${
                message.role === "user" ? "text-blue-100" : "text-gray-500"
              }`}>
                {new Date(message.timestamp).toLocaleTimeString()}
                {message.type === "audio" && " • Audio"}
              </div>
            </div>
          </div>
        ))}
        
        {/* Show typing indicator for streaming messages */}
        {messages.length > 0 && 
         messages[messages.length - 1]?.role === "assistant" && 
         messages[messages.length - 1]?.id.startsWith("temp-") && (
          <div className="flex justify-start">
            <div className="max-w-xs lg:max-w-md px-4 py-3 rounded-lg bg-gray-50 border border-gray-200">
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                </div>
                <span className="text-xs text-gray-500">AI is responding...</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
