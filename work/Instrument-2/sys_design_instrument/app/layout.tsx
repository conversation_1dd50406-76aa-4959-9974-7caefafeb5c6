import type { Metada<PERSON> } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Realtime Interview Assistant",
  description: "Speech-to-speech assistant for mock system design interviews",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="antialiased">
        {children}
      </body>
    </html>
  );
}
