"use client";

export interface RealtimeEvent {
  type: string;
  [key: string]: any;
}

export interface RealtimeConfig {
  apiKey: string;
  model?: string;
  voice?: string;
  instructions?: string;
  onEvent?: (event: RealtimeEvent) => void;
  onError?: (error: Error) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
}

export class RealtimeWebSocket {
  private ws: WebSocket | null = null;
  private config: RealtimeConfig;
  private isConnected = false;
  private sessionStartTime: number = 0;
  private readonly SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  private timeoutId: NodeJS.Timeout | null = null;

  constructor(config: RealtimeConfig) {
    this.config = config;
  }

  async connect(): Promise<void> {
    try {
      // Get ephemeral key
      const response = await fetch("/api/realtime/ephemeral", {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error(`Failed to get ephemeral key: ${await response.text()}`);
      }

      const { ephemeralKey } = await response.json();

      // Connect to WebSocket
      const wsUrl = `wss://api.openai.com/v1/realtime?model=${this.config.model || "gpt-4o-realtime-preview-2024-10-01"}`;
      this.ws = new WebSocket(wsUrl, [
        "realtime",
        `Bearer.${ephemeralKey}`,
        "openai-beta.realtime-v1"
      ]);

      this.ws.onopen = () => {
        this.isConnected = true;
        this.sessionStartTime = Date.now();
        this.startSessionTimeout();
        this.initializeSession();
        this.config.onConnect?.();
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleEvent(data);
        } catch (error) {
          console.error("Failed to parse WebSocket message:", error);
        }
      };

      this.ws.onclose = () => {
        this.isConnected = false;
        this.clearSessionTimeout();
        this.config.onDisconnect?.();
      };

      this.ws.onerror = (error) => {
        this.config.onError?.(new Error("WebSocket error"));
      };

    } catch (error) {
      this.config.onError?.(error as Error);
    }
  }

  private initializeSession(): void {
    this.sendEvent({
      type: "session.update",
      session: {
        modalities: ["text", "audio"],
        voice: this.config.voice || "alloy",
        instructions: this.config.instructions || "You are a helpful assistant.",
        turn_detection: null, // Disable server VAD for manual control
        input_audio_transcription: {
          model: "whisper-1"
        },
        tools: []
      }
    });
  }

  private handleEvent(event: RealtimeEvent): void {
    this.config.onEvent?.(event);
  }

  sendEvent(event: RealtimeEvent): void {
    if (this.ws && this.isConnected) {
      this.ws.send(JSON.stringify(event));
    }
  }

  // Audio input methods
  appendAudioBuffer(audioData: string): void {
    this.sendEvent({
      type: "input_audio_buffer.append",
      audio: audioData
    });
  }

  commitAudioBuffer(): void {
    this.sendEvent({
      type: "input_audio_buffer.commit"
    });
  }

  clearAudioBuffer(): void {
    this.sendEvent({
      type: "input_audio_buffer.clear"
    });
  }

  // Response control
  createResponse(): void {
    this.sendEvent({
      type: "response.create"
    });
  }

  cancelResponse(): void {
    this.sendEvent({
      type: "response.cancel"
    });
  }

  // Session management
  private startSessionTimeout(): void {
    this.timeoutId = setTimeout(() => {
      this.reconnect();
    }, this.SESSION_TIMEOUT);
  }

  private clearSessionTimeout(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  private async reconnect(): Promise<void> {
    this.disconnect();
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    await this.connect();
  }

  disconnect(): void {
    this.clearSessionTimeout();
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  getSessionDuration(): number {
    return this.sessionStartTime ? Date.now() - this.sessionStartTime : 0;
  }
}
