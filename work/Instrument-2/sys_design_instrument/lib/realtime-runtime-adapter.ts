"use client";

// Runtime adapter for realtime conversation
import { RealtimeWebSocket, RealtimeEvent } from "./realtime-websocket";

export interface RealtimeMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: number;
  type: "text" | "audio";
}

export class RealtimeRuntimeAdapter {
  private _messages: RealtimeMessage[] = [];
  private subscribers = new Set<() => void>();
  private wsRef: React.MutableRefObject<RealtimeWebSocket | null>;
  private messageIdCounter = 0;
  private currentAssistantMessage = "";

  constructor(wsRef: React.MutableRefObject<RealtimeWebSocket | null>) {
    this.wsRef = wsRef;
  }

  // Subscribe to runtime changes
  subscribe(callback: () => void): () => void {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  private notifySubscribers() {
    this.subscribers.forEach(callback => callback());
  }

  // Get current thread messages
  get messages(): RealtimeMessage[] {
    return this._messages;
  }

  // Add message to thread
  addMessage(message: RealtimeMessage) {
    this._messages.push(message);
    this.notifySubscribers();
  }

  // Update current assistant message (for streaming)
  updateCurrentAssistantMessage(content: string) {
    this.currentAssistantMessage = content;
    
    // Find or create current assistant message
    const lastMessage = this._messages[this._messages.length - 1];
    if (lastMessage && lastMessage.role === "assistant" && lastMessage.id.startsWith("temp-")) {
      // Update existing temporary message
      lastMessage.content = content;
    } else {
      // Create new temporary message
      this._messages.push({
        id: `temp-assistant-${this.messageIdCounter++}`,
        role: "assistant",
        content,
        timestamp: Date.now(),
        type: "text",
      });
    }
    
    this.notifySubscribers();
  }

  // Finalize current assistant message
  finalizeAssistantMessage() {
    const lastMessage = this._messages[this._messages.length - 1];
    if (lastMessage && lastMessage.role === "assistant" && lastMessage.id.startsWith("temp-")) {
      // Convert temporary message to permanent
      lastMessage.id = `assistant-${this.messageIdCounter++}`;
    }
    this.currentAssistantMessage = "";
    this.notifySubscribers();
  }

  // Handle realtime events
  handleRealtimeEvent(event: RealtimeEvent) {
    switch (event.type) {
      case "conversation.item.input_audio_transcription.completed":
        // Add user message
        this.addMessage({
          id: `user-${this.messageIdCounter++}`,
          role: "user",
          content: event.transcript,
          timestamp: Date.now(),
          type: "text",
        });
        break;

      case "response.audio_transcript.delta":
        // Update streaming assistant message
        this.updateCurrentAssistantMessage(
          this.currentAssistantMessage + (event.delta || "")
        );
        break;

      case "response.audio_transcript.done":
        // Finalize assistant message
        this.finalizeAssistantMessage();
        break;

      case "response.done":
        // Ensure message is finalized
        this.finalizeAssistantMessage();
        break;
    }
  }

  // Utility methods
  cancel(): void {
    // Cancel current response
    this.wsRef.current?.cancelResponse();
  }

  get isRunning(): boolean {
    return this.wsRef.current?.getConnectionStatus() || false;
  }

  // Clear conversation
  clearMessages() {
    this._messages = [];
    this.currentAssistantMessage = "";
    this.notifySubscribers();
  }

  // Get message count
  get messageCount(): number {
    return this._messages.length;
  }
}
