"use client";

export interface AudioConfig {
  sampleRate?: number;
  channels?: number;
  echoCancellation?: boolean;
  noiseSuppression?: boolean;
  autoGainControl?: boolean;
}

export class AudioPipeline {
  private audioContext: AudioContext | null = null;
  private mediaStream: MediaStream | null = null;
  private mediaRecorder: MediaRecorder | null = null;
  private gainNode: GainNode | null = null;
  private isRecording = false;
  private isMuted = false;
  private playbackRate = 1.0;
  private audioQueue: AudioBuffer[] = [];
  private isPlaying = false;
  private config: AudioConfig;

  private onAudioData?: (audioData: string) => void;
  private onError?: (error: Error) => void;

  constructor(config: AudioConfig = {}) {
    this.config = {
      sampleRate: 24000,
      channels: 1,
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true,
      ...config,
    };
  }

  async initialize(): Promise<void> {
    try {
      // Initialize AudioContext
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: this.config.sampleRate,
      });

      // Create gain node for volume control
      this.gainNode = this.audioContext.createGain();
      this.gainNode.connect(this.audioContext.destination);

      // Get microphone access
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.config.sampleRate,
          channelCount: this.config.channels,
          echoCancellation: this.config.echoCancellation,
          noiseSuppression: this.config.noiseSuppression,
          autoGainControl: this.config.autoGainControl,
        },
      });

      // Setup MediaRecorder for continuous audio capture
      this.setupMediaRecorder();

    } catch (error) {
      this.onError?.(error as Error);
      throw error;
    }
  }

  private setupMediaRecorder(): void {
    if (!this.mediaStream) return;

    // Use webm/opus for better compression and compatibility
    const mimeType = MediaRecorder.isTypeSupported('audio/webm;codecs=opus') 
      ? 'audio/webm;codecs=opus' 
      : 'audio/webm';

    this.mediaRecorder = new MediaRecorder(this.mediaStream, {
      mimeType,
      audioBitsPerSecond: 64000, // 64kbps for good quality/size balance
    });

    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.processAudioChunk(event.data);
      }
    };

    this.mediaRecorder.onerror = (event) => {
      this.onError?.(new Error('MediaRecorder error'));
    };
  }

  private async processAudioChunk(blob: Blob): Promise<void> {
    try {
      // Convert blob to ArrayBuffer
      const arrayBuffer = await blob.arrayBuffer();
      
      // Convert to PCM 16-bit format expected by OpenAI
      const audioBuffer = await this.audioContext!.decodeAudioData(arrayBuffer);
      const pcmData = this.convertToPCM16(audioBuffer);
      
      // Convert to base64
      const base64Audio = this.arrayBufferToBase64(pcmData);
      
      // Send to callback
      this.onAudioData?.(base64Audio);
    } catch (error) {
      console.error('Error processing audio chunk:', error);
    }
  }

  private convertToPCM16(audioBuffer: AudioBuffer): ArrayBuffer {
    const length = audioBuffer.length;
    const pcm16 = new Int16Array(length);
    const channelData = audioBuffer.getChannelData(0); // Use first channel

    for (let i = 0; i < length; i++) {
      // Convert float32 (-1 to 1) to int16 (-32768 to 32767)
      const sample = Math.max(-1, Math.min(1, channelData[i]));
      pcm16[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
    }

    return pcm16.buffer;
  }

  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  // Audio input control
  startRecording(onAudioData: (audioData: string) => void): void {
    if (!this.mediaRecorder || this.isRecording) return;

    this.onAudioData = onAudioData;
    this.isRecording = true;
    
    // Start recording with small chunks for real-time processing
    this.mediaRecorder.start(100); // 100ms chunks
  }

  stopRecording(): void {
    if (!this.mediaRecorder || !this.isRecording) return;

    this.isRecording = false;
    this.mediaRecorder.stop();
  }

  // Audio output control
  async playAudioDelta(base64Audio: string): Promise<void> {
    if (!this.audioContext || this.isMuted) return;

    try {
      // Decode base64 to ArrayBuffer
      const binaryString = atob(base64Audio);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Convert PCM16 to AudioBuffer
      const audioBuffer = await this.pcm16ToAudioBuffer(bytes.buffer);
      
      // Add to queue or play immediately
      if (this.isPlaying) {
        this.audioQueue.push(audioBuffer);
      } else {
        this.playAudioBuffer(audioBuffer);
      }
    } catch (error) {
      console.error('Error playing audio delta:', error);
    }
  }

  private async pcm16ToAudioBuffer(pcmData: ArrayBuffer): Promise<AudioBuffer> {
    const pcm16 = new Int16Array(pcmData);
    const audioBuffer = this.audioContext!.createBuffer(
      1, // mono
      pcm16.length,
      this.config.sampleRate!
    );

    const channelData = audioBuffer.getChannelData(0);
    for (let i = 0; i < pcm16.length; i++) {
      // Convert int16 to float32
      channelData[i] = pcm16[i] / (pcm16[i] < 0 ? 0x8000 : 0x7FFF);
    }

    return audioBuffer;
  }

  private playAudioBuffer(audioBuffer: AudioBuffer): void {
    if (!this.audioContext || !this.gainNode) return;

    const source = this.audioContext.createBufferSource();
    source.buffer = audioBuffer;
    source.playbackRate.value = this.playbackRate;
    source.connect(this.gainNode);

    this.isPlaying = true;
    source.onended = () => {
      this.isPlaying = false;
      // Play next in queue
      if (this.audioQueue.length > 0) {
        const nextBuffer = this.audioQueue.shift()!;
        this.playAudioBuffer(nextBuffer);
      }
    };

    source.start();
  }

  // Audio controls
  setPlaybackRate(rate: number): void {
    this.playbackRate = Math.max(0.25, Math.min(4.0, rate));
  }

  setMuted(muted: boolean): void {
    this.isMuted = muted;
    if (this.gainNode) {
      this.gainNode.gain.value = muted ? 0 : 1;
    }
  }

  getMuted(): boolean {
    return this.isMuted;
  }

  getPlaybackRate(): number {
    return this.playbackRate;
  }

  // Cleanup
  destroy(): void {
    this.stopRecording();
    
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.audioQueue = [];
    this.isPlaying = false;
  }

  // Event handlers
  setOnError(callback: (error: Error) => void): void {
    this.onError = callback;
  }
}
