/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./components/audio-controls.tsx":
/*!***************************************!*\
  !*** ./components/audio-controls.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioControls: () => (/* binding */ AudioControls)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* __next_internal_client_entry_do_not_use__ AudioControls auto */ \n\nfunction AudioControls(param) {\n    let { isMuted, playbackRate, onMuteToggle, onPlaybackRateChange } = param;\n    const playbackRates = [\n        0.5,\n        0.75,\n        1.0,\n        1.25,\n        1.5,\n        2.0\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onMuteToggle,\n                className: \"p-2 rounded-md transition-colors \".concat(isMuted ? \"bg-red-100 text-red-700 hover:bg-red-200\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                title: isMuted ? \"Unmute (M)\" : \"Mute (M)\",\n                children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    size: 20\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 20\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    size: 20\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 44\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: \"Speed:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: playbackRate,\n                        onChange: (e)=>onPlaybackRateChange(parseFloat(e.target.value)),\n                        className: \"px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                        children: playbackRates.map((rate)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: rate,\n                                children: [\n                                    rate,\n                                    \"x\"\n                                ]\n                            }, rate, true, {\n                                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-500\",\n                children: isMuted ? \"Audio muted\" : \"Playing at \".concat(playbackRate, \"x speed\")\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_c = AudioControls;\nvar _c;\n$RefreshReg$(_c, \"AudioControls\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/audio-controls.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/connection-status.tsx":
/*!******************************************!*\
  !*** ./components/connection-status.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionStatus: () => (/* binding */ ConnectionStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ConnectionStatus auto */ \nfunction ConnectionStatus(param) {\n    let { isConnected, isConnecting, onConnect, onDisconnect } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-3 h-3 rounded-full \".concat(isConnected ? \"bg-green-500\" : isConnecting ? \"bg-yellow-500 animate-pulse\" : \"bg-red-500\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/connection-status.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: isConnected ? \"Connected\" : isConnecting ? \"Connecting...\" : \"Disconnected\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/connection-status.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/connection-status.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: isConnected ? onDisconnect : onConnect,\n                disabled: isConnecting,\n                className: \"px-4 py-2 rounded-md text-sm font-medium transition-colors \".concat(isConnected ? \"bg-red-100 text-red-700 hover:bg-red-200\" : \"bg-blue-100 text-blue-700 hover:bg-blue-200\", \" disabled:opacity-50 disabled:cursor-not-allowed\"),\n                children: isConnecting ? \"Connecting...\" : isConnected ? \"Disconnect\" : \"Connect\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/connection-status.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/connection-status.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_c = ConnectionStatus;\nvar _c;\n$RefreshReg$(_c, \"ConnectionStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/connection-status.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/realtime-assistant.tsx":
/*!*******************************************!*\
  !*** ./components/realtime-assistant.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeAssistant: () => (/* binding */ RealtimeAssistant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_realtime_websocket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/realtime-websocket */ \"(app-pages-browser)/./lib/realtime-websocket.ts\");\n/* harmony import */ var _lib_audio_pipeline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/audio-pipeline */ \"(app-pages-browser)/./lib/audio-pipeline.ts\");\n/* harmony import */ var _lib_realtime_runtime_adapter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/realtime-runtime-adapter */ \"(app-pages-browser)/./lib/realtime-runtime-adapter.ts\");\n/* harmony import */ var _audio_controls__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./audio-controls */ \"(app-pages-browser)/./components/audio-controls.tsx\");\n/* harmony import */ var _connection_status__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./connection-status */ \"(app-pages-browser)/./components/connection-status.tsx\");\n/* harmony import */ var _trigger_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./trigger-button */ \"(app-pages-browser)/./components/trigger-button.tsx\");\n/* harmony import */ var _simple_thread__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./simple-thread */ \"(app-pages-browser)/./components/simple-thread.tsx\");\n/* __next_internal_client_entry_do_not_use__ RealtimeAssistant auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Interview-specific system prompt\nconst INTERVIEW_SYSTEM_PROMPT = 'You are an expert system design interviewer. Listen to the conversation between the interviewee and interviewer but only respond with questions, clarifications, or feedback when the interviewee says \"AI, your turn\" or similar trigger phrases. \\n\\nMaintain context across exchanges and provide insightful questions that help evaluate the candidate\\'s system design thinking. Focus on:\\n- Scalability considerations\\n- Trade-offs and design decisions\\n- Clarifying requirements\\n- Probing deeper into architectural choices\\n- Identifying potential bottlenecks\\n\\nKeep responses concise and interview-appropriate.';\nfunction RealtimeAssistant() {\n    _s();\n    // Connection state\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Audio state\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [playbackRate, setPlaybackRate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    // Refs\n    const wsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioPipelineRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const runtimeAdapterRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize runtime adapter\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RealtimeAssistant.useEffect\": ()=>{\n            runtimeAdapterRef.current = new _lib_realtime_runtime_adapter__WEBPACK_IMPORTED_MODULE_4__.RealtimeRuntimeAdapter(wsRef);\n        }\n    }[\"RealtimeAssistant.useEffect\"], []);\n    // Initialize audio pipeline\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RealtimeAssistant.useEffect\": ()=>{\n            const initAudio = {\n                \"RealtimeAssistant.useEffect.initAudio\": async ()=>{\n                    try {\n                        const pipeline = new _lib_audio_pipeline__WEBPACK_IMPORTED_MODULE_3__.AudioPipeline({\n                            sampleRate: 24000,\n                            channels: 1,\n                            echoCancellation: true\n                        });\n                        pipeline.setOnError({\n                            \"RealtimeAssistant.useEffect.initAudio\": (error)=>{\n                                setError(\"Audio error: \".concat(error.message));\n                            }\n                        }[\"RealtimeAssistant.useEffect.initAudio\"]);\n                        await pipeline.initialize();\n                        audioPipelineRef.current = pipeline;\n                    } catch (error) {\n                        setError(\"Failed to initialize audio: \".concat(error.message));\n                    }\n                }\n            }[\"RealtimeAssistant.useEffect.initAudio\"];\n            initAudio();\n            return ({\n                \"RealtimeAssistant.useEffect\": ()=>{\n                    var _audioPipelineRef_current;\n                    (_audioPipelineRef_current = audioPipelineRef.current) === null || _audioPipelineRef_current === void 0 ? void 0 : _audioPipelineRef_current.destroy();\n                }\n            })[\"RealtimeAssistant.useEffect\"];\n        }\n    }[\"RealtimeAssistant.useEffect\"], []);\n    // WebSocket event handler\n    const handleRealtimeEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RealtimeAssistant.useCallback[handleRealtimeEvent]\": (event)=>{\n            var // Forward event to runtime adapter\n            _runtimeAdapterRef_current;\n            console.log(\"Realtime event:\", event);\n            (_runtimeAdapterRef_current = runtimeAdapterRef.current) === null || _runtimeAdapterRef_current === void 0 ? void 0 : _runtimeAdapterRef_current.handleRealtimeEvent(event);\n            switch(event.type){\n                case \"session.created\":\n                    console.log(\"Session created\");\n                    break;\n                case \"input_audio_buffer.speech_started\":\n                    console.log(\"Speech started\");\n                    break;\n                case \"input_audio_buffer.speech_stopped\":\n                    console.log(\"Speech stopped\");\n                    break;\n                case \"response.audio.delta\":\n                    // Play audio delta\n                    if (event.delta && audioPipelineRef.current) {\n                        audioPipelineRef.current.playAudioDelta(event.delta);\n                    }\n                    break;\n                case \"error\":\n                    var _event_error;\n                    setError(\"Realtime API error: \".concat(((_event_error = event.error) === null || _event_error === void 0 ? void 0 : _event_error.message) || \"Unknown error\"));\n                    break;\n                default:\n                    break;\n            }\n        }\n    }[\"RealtimeAssistant.useCallback[handleRealtimeEvent]\"], []);\n    // Connect to realtime API\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RealtimeAssistant.useCallback[connect]\": async ()=>{\n            if (isConnecting || isConnected) return;\n            setIsConnecting(true);\n            setError(null);\n            try {\n                const ws = new _lib_realtime_websocket__WEBPACK_IMPORTED_MODULE_2__.RealtimeWebSocket({\n                    apiKey: \"\",\n                    model: \"gpt-4o-realtime-preview-2024-10-01\",\n                    voice: \"alloy\",\n                    instructions: INTERVIEW_SYSTEM_PROMPT,\n                    onEvent: handleRealtimeEvent,\n                    onError: {\n                        \"RealtimeAssistant.useCallback[connect]\": (error)=>setError(error.message)\n                    }[\"RealtimeAssistant.useCallback[connect]\"],\n                    onConnect: {\n                        \"RealtimeAssistant.useCallback[connect]\": ()=>{\n                            setIsConnected(true);\n                            setIsConnecting(false);\n                            startRecording();\n                        }\n                    }[\"RealtimeAssistant.useCallback[connect]\"],\n                    onDisconnect: {\n                        \"RealtimeAssistant.useCallback[connect]\": ()=>{\n                            setIsConnected(false);\n                            setIsRecording(false);\n                        }\n                    }[\"RealtimeAssistant.useCallback[connect]\"]\n                });\n                await ws.connect();\n                wsRef.current = ws;\n            } catch (error) {\n                setError(error.message);\n                setIsConnecting(false);\n            }\n        }\n    }[\"RealtimeAssistant.useCallback[connect]\"], [\n        isConnecting,\n        isConnected,\n        handleRealtimeEvent\n    ]);\n    // Disconnect\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RealtimeAssistant.useCallback[disconnect]\": ()=>{\n            var _wsRef_current;\n            (_wsRef_current = wsRef.current) === null || _wsRef_current === void 0 ? void 0 : _wsRef_current.disconnect();\n            wsRef.current = null;\n            setIsConnected(false);\n            setIsRecording(false);\n        }\n    }[\"RealtimeAssistant.useCallback[disconnect]\"], []);\n    // Start continuous recording\n    const startRecording = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RealtimeAssistant.useCallback[startRecording]\": ()=>{\n            if (!audioPipelineRef.current || !wsRef.current || isRecording) return;\n            audioPipelineRef.current.startRecording({\n                \"RealtimeAssistant.useCallback[startRecording]\": (audioData)=>{\n                    var _wsRef_current;\n                    (_wsRef_current = wsRef.current) === null || _wsRef_current === void 0 ? void 0 : _wsRef_current.appendAudioBuffer(audioData);\n                }\n            }[\"RealtimeAssistant.useCallback[startRecording]\"]);\n            setIsRecording(true);\n        }\n    }[\"RealtimeAssistant.useCallback[startRecording]\"], [\n        isRecording\n    ]);\n    // Manual trigger for AI response\n    const triggerResponse = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RealtimeAssistant.useCallback[triggerResponse]\": ()=>{\n            if (!wsRef.current || !isConnected) return;\n            // Commit the audio buffer and request response\n            wsRef.current.commitAudioBuffer();\n            wsRef.current.createResponse();\n        }\n    }[\"RealtimeAssistant.useCallback[triggerResponse]\"], [\n        isConnected\n    ]);\n    // Audio controls\n    const handleMuteToggle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RealtimeAssistant.useCallback[handleMuteToggle]\": ()=>{\n            var _audioPipelineRef_current;\n            const newMuted = !isMuted;\n            setIsMuted(newMuted);\n            (_audioPipelineRef_current = audioPipelineRef.current) === null || _audioPipelineRef_current === void 0 ? void 0 : _audioPipelineRef_current.setMuted(newMuted);\n        }\n    }[\"RealtimeAssistant.useCallback[handleMuteToggle]\"], [\n        isMuted\n    ]);\n    const handlePlaybackRateChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RealtimeAssistant.useCallback[handlePlaybackRateChange]\": (rate)=>{\n            var _audioPipelineRef_current;\n            setPlaybackRate(rate);\n            (_audioPipelineRef_current = audioPipelineRef.current) === null || _audioPipelineRef_current === void 0 ? void 0 : _audioPipelineRef_current.setPlaybackRate(rate);\n        }\n    }[\"RealtimeAssistant.useCallback[handlePlaybackRateChange]\"], []);\n    // Keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RealtimeAssistant.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"RealtimeAssistant.useEffect.handleKeyPress\": (event)=>{\n                    if (event.code === \"Space\" && !event.repeat) {\n                        event.preventDefault();\n                        triggerResponse();\n                    } else if (event.code === \"KeyM\" && !event.repeat) {\n                        event.preventDefault();\n                        handleMuteToggle();\n                    }\n                }\n            }[\"RealtimeAssistant.useEffect.handleKeyPress\"];\n            window.addEventListener(\"keydown\", handleKeyPress);\n            return ({\n                \"RealtimeAssistant.useEffect\": ()=>window.removeEventListener(\"keydown\", handleKeyPress)\n            })[\"RealtimeAssistant.useEffect\"];\n        }\n    }[\"RealtimeAssistant.useEffect\"], [\n        triggerResponse,\n        handleMuteToggle\n    ]);\n    if (!runtimeAdapterRef.current) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n            lineNumber: 203,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white border-b px-6 py-4 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold\",\n                            children: \"Realtime Interview Assistant\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_connection_status__WEBPACK_IMPORTED_MODULE_6__.ConnectionStatus, {\n                            isConnected: isConnected,\n                            isConnecting: isConnecting,\n                            onConnect: connect,\n                            onDisconnect: disconnect\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border-l-4 border-red-400 p-4 mx-6 mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-700\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setError(null),\n                            className: \"text-red-600 hover:text-red-800 text-sm mt-2\",\n                            children: \"Dismiss\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_simple_thread__WEBPACK_IMPORTED_MODULE_8__.SimpleThread, {\n                    runtime: runtimeAdapterRef.current\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white border-t p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_audio_controls__WEBPACK_IMPORTED_MODULE_5__.AudioControls, {\n                                    isMuted: isMuted,\n                                    playbackRate: playbackRate,\n                                    onMuteToggle: handleMuteToggle,\n                                    onPlaybackRateChange: handlePlaybackRateChange\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trigger_button__WEBPACK_IMPORTED_MODULE_7__.TriggerButton, {\n                                    onTrigger: triggerResponse,\n                                    disabled: !isConnected,\n                                    isRecording: isRecording\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 mt-2 text-center\",\n                            children: [\n                                \"Press \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                    className: \"px-1 py-0.5 bg-gray-100 rounded\",\n                                    children: \"Space\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 19\n                                }, this),\n                                \" to trigger AI response •\",\n                                \" \",\n                                \"Press \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                    className: \"px-1 py-0.5 bg-gray-100 rounded\",\n                                    children: \"M\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 19\n                                }, this),\n                                \" to mute/unmute\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n            lineNumber: 209,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n_s(RealtimeAssistant, \"ZuShys3uJHizVQMf5zSg00vgBEI=\");\n_c = RealtimeAssistant;\nvar _c;\n$RefreshReg$(_c, \"RealtimeAssistant\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/realtime-assistant.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/simple-thread.tsx":
/*!**************************************!*\
  !*** ./components/simple-thread.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleThread: () => (/* binding */ SimpleThread)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SimpleThread auto */ \nvar _s = $RefreshSig$();\n\nfunction SimpleThread(param) {\n    let { runtime } = param;\n    var _messages_, _messages_1;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleThread.useEffect\": ()=>{\n            const updateMessages = {\n                \"SimpleThread.useEffect.updateMessages\": ()=>{\n                    setMessages([\n                        ...runtime.messages\n                    ]);\n                }\n            }[\"SimpleThread.useEffect.updateMessages\"];\n            // Subscribe to runtime changes\n            const unsubscribe = runtime.subscribe(updateMessages);\n            // Initial load\n            updateMessages();\n            return unsubscribe;\n        }\n    }[\"SimpleThread.useEffect\"], [\n        runtime\n    ]);\n    if (messages.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center text-gray-500\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium mb-2\",\n                        children: \"Ready to start\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm\",\n                        children: \"Connect and start speaking. Press spacebar to trigger AI responses.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 overflow-y-auto p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4 max-w-4xl mx-auto\",\n            children: [\n                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-xs lg:max-w-md px-4 py-3 rounded-lg \".concat(message.role === \"user\" ? \"bg-blue-600 text-white\" : \"bg-white border border-gray-200 shadow-sm\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm leading-relaxed whitespace-pre-wrap\",\n                                    children: message.content\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs mt-2 \".concat(message.role === \"user\" ? \"text-blue-100\" : \"text-gray-500\"),\n                                    children: [\n                                        new Date(message.timestamp).toLocaleTimeString(),\n                                        message.type === \"audio\" && \" • Audio\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this)\n                    }, message.id, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)),\n                messages.length > 0 && ((_messages_ = messages[messages.length - 1]) === null || _messages_ === void 0 ? void 0 : _messages_.role) === \"assistant\" && ((_messages_1 = messages[messages.length - 1]) === null || _messages_1 === void 0 ? void 0 : _messages_1.id.startsWith(\"temp-\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-start\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-xs lg:max-w-md px-4 py-3 rounded-lg bg-gray-50 border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: \"0.1s\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: \"0.2s\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"AI is responding...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleThread, \"RyA59LRbn9goj/9N7rELX+NWNVI=\");\n_c = SimpleThread;\nvar _c;\n$RefreshReg$(_c, \"SimpleThread\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/simple-thread.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/trigger-button.tsx":
/*!***************************************!*\
  !*** ./components/trigger-button.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TriggerButton: () => (/* binding */ TriggerButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* __next_internal_client_entry_do_not_use__ TriggerButton auto */ \n\nfunction TriggerButton(param) {\n    let { onTrigger, disabled, isRecording } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        className: \"w-4 h-4 text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/trigger-button.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-4 h-4 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/trigger-button.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600\",\n                        children: isRecording ? \"Listening...\" : \"Not recording\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/trigger-button.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/trigger-button.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onTrigger,\n                disabled: disabled,\n                className: \"px-6 py-3 rounded-lg font-medium transition-all \".concat(disabled ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 shadow-md hover:shadow-lg\"),\n                title: \"Trigger AI Response (Spacebar)\",\n                children: \"\\uD83C\\uDFA4 Ask AI\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/trigger-button.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/trigger-button.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_c = TriggerButton;\nvar _c;\n$RefreshReg$(_c, \"TriggerButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/trigger-button.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/audio-pipeline.ts":
/*!*******************************!*\
  !*** ./lib/audio-pipeline.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioPipeline: () => (/* binding */ AudioPipeline)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ AudioPipeline auto */ class AudioPipeline {\n    async initialize() {\n        try {\n            // Initialize AudioContext\n            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({\n                sampleRate: this.config.sampleRate\n            });\n            // Create gain node for volume control\n            this.gainNode = this.audioContext.createGain();\n            this.gainNode.connect(this.audioContext.destination);\n            // Get microphone access\n            this.mediaStream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: this.config.sampleRate,\n                    channelCount: this.config.channels,\n                    echoCancellation: this.config.echoCancellation,\n                    noiseSuppression: this.config.noiseSuppression,\n                    autoGainControl: this.config.autoGainControl\n                }\n            });\n            // Setup MediaRecorder for continuous audio capture\n            this.setupMediaRecorder();\n        } catch (error) {\n            var _this_onError, _this;\n            (_this_onError = (_this = this).onError) === null || _this_onError === void 0 ? void 0 : _this_onError.call(_this, error);\n            throw error;\n        }\n    }\n    setupMediaRecorder() {\n        if (!this.mediaStream) return;\n        // Use webm/opus for better compression and compatibility\n        const mimeType = MediaRecorder.isTypeSupported('audio/webm;codecs=opus') ? 'audio/webm;codecs=opus' : 'audio/webm';\n        this.mediaRecorder = new MediaRecorder(this.mediaStream, {\n            mimeType,\n            audioBitsPerSecond: 64000\n        });\n        this.mediaRecorder.ondataavailable = (event)=>{\n            if (event.data.size > 0) {\n                this.processAudioChunk(event.data);\n            }\n        };\n        this.mediaRecorder.onerror = (event)=>{\n            var _this_onError, _this;\n            (_this_onError = (_this = this).onError) === null || _this_onError === void 0 ? void 0 : _this_onError.call(_this, new Error('MediaRecorder error'));\n        };\n    }\n    async processAudioChunk(blob) {\n        try {\n            var // Send to callback\n            _this_onAudioData, _this;\n            // Convert blob to ArrayBuffer\n            const arrayBuffer = await blob.arrayBuffer();\n            // Convert to PCM 16-bit format expected by OpenAI\n            const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);\n            const pcmData = this.convertToPCM16(audioBuffer);\n            // Convert to base64\n            const base64Audio = this.arrayBufferToBase64(pcmData);\n            (_this_onAudioData = (_this = this).onAudioData) === null || _this_onAudioData === void 0 ? void 0 : _this_onAudioData.call(_this, base64Audio);\n        } catch (error) {\n            console.error('Error processing audio chunk:', error);\n        }\n    }\n    convertToPCM16(audioBuffer) {\n        const length = audioBuffer.length;\n        const pcm16 = new Int16Array(length);\n        const channelData = audioBuffer.getChannelData(0); // Use first channel\n        for(let i = 0; i < length; i++){\n            // Convert float32 (-1 to 1) to int16 (-32768 to 32767)\n            const sample = Math.max(-1, Math.min(1, channelData[i]));\n            pcm16[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;\n        }\n        return pcm16.buffer;\n    }\n    arrayBufferToBase64(buffer) {\n        const bytes = new Uint8Array(buffer);\n        let binary = '';\n        for(let i = 0; i < bytes.byteLength; i++){\n            binary += String.fromCharCode(bytes[i]);\n        }\n        return btoa(binary);\n    }\n    // Audio input control\n    startRecording(onAudioData) {\n        if (!this.mediaRecorder || this.isRecording) return;\n        this.onAudioData = onAudioData;\n        this.isRecording = true;\n        // Start recording with small chunks for real-time processing\n        this.mediaRecorder.start(100); // 100ms chunks\n    }\n    stopRecording() {\n        if (!this.mediaRecorder || !this.isRecording) return;\n        this.isRecording = false;\n        this.mediaRecorder.stop();\n    }\n    // Audio output control\n    async playAudioDelta(base64Audio) {\n        if (!this.audioContext || this.isMuted) return;\n        try {\n            // Decode base64 to ArrayBuffer\n            const binaryString = atob(base64Audio);\n            const bytes = new Uint8Array(binaryString.length);\n            for(let i = 0; i < binaryString.length; i++){\n                bytes[i] = binaryString.charCodeAt(i);\n            }\n            // Convert PCM16 to AudioBuffer\n            const audioBuffer = await this.pcm16ToAudioBuffer(bytes.buffer);\n            // Add to queue or play immediately\n            if (this.isPlaying) {\n                this.audioQueue.push(audioBuffer);\n            } else {\n                this.playAudioBuffer(audioBuffer);\n            }\n        } catch (error) {\n            console.error('Error playing audio delta:', error);\n        }\n    }\n    async pcm16ToAudioBuffer(pcmData) {\n        const pcm16 = new Int16Array(pcmData);\n        const audioBuffer = this.audioContext.createBuffer(1, pcm16.length, this.config.sampleRate);\n        const channelData = audioBuffer.getChannelData(0);\n        for(let i = 0; i < pcm16.length; i++){\n            // Convert int16 to float32\n            channelData[i] = pcm16[i] / (pcm16[i] < 0 ? 0x8000 : 0x7FFF);\n        }\n        return audioBuffer;\n    }\n    playAudioBuffer(audioBuffer) {\n        if (!this.audioContext || !this.gainNode) return;\n        const source = this.audioContext.createBufferSource();\n        source.buffer = audioBuffer;\n        source.playbackRate.value = this.playbackRate;\n        source.connect(this.gainNode);\n        this.isPlaying = true;\n        source.onended = ()=>{\n            this.isPlaying = false;\n            // Play next in queue\n            if (this.audioQueue.length > 0) {\n                const nextBuffer = this.audioQueue.shift();\n                this.playAudioBuffer(nextBuffer);\n            }\n        };\n        source.start();\n    }\n    // Audio controls\n    setPlaybackRate(rate) {\n        this.playbackRate = Math.max(0.25, Math.min(4.0, rate));\n    }\n    setMuted(muted) {\n        this.isMuted = muted;\n        if (this.gainNode) {\n            this.gainNode.gain.value = muted ? 0 : 1;\n        }\n    }\n    getMuted() {\n        return this.isMuted;\n    }\n    getPlaybackRate() {\n        return this.playbackRate;\n    }\n    // Cleanup\n    destroy() {\n        this.stopRecording();\n        if (this.mediaStream) {\n            this.mediaStream.getTracks().forEach((track)=>track.stop());\n            this.mediaStream = null;\n        }\n        if (this.audioContext) {\n            this.audioContext.close();\n            this.audioContext = null;\n        }\n        this.audioQueue = [];\n        this.isPlaying = false;\n    }\n    // Event handlers\n    setOnError(callback) {\n        this.onError = callback;\n    }\n    constructor(config = {}){\n        this.audioContext = null;\n        this.mediaStream = null;\n        this.mediaRecorder = null;\n        this.gainNode = null;\n        this.isRecording = false;\n        this.isMuted = false;\n        this.playbackRate = 1.0;\n        this.audioQueue = [];\n        this.isPlaying = false;\n        this.config = {\n            sampleRate: 24000,\n            channels: 1,\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true,\n            ...config\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/audio-pipeline.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/realtime-runtime-adapter.ts":
/*!*****************************************!*\
  !*** ./lib/realtime-runtime-adapter.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeRuntimeAdapter: () => (/* binding */ RealtimeRuntimeAdapter)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ RealtimeRuntimeAdapter auto */ class RealtimeRuntimeAdapter {\n    // Subscribe to runtime changes\n    subscribe(callback) {\n        this.subscribers.add(callback);\n        return ()=>this.subscribers.delete(callback);\n    }\n    notifySubscribers() {\n        this.subscribers.forEach((callback)=>callback());\n    }\n    // Get current thread messages\n    get messages() {\n        return this._messages;\n    }\n    // Add message to thread\n    addMessage(message) {\n        this._messages.push(message);\n        this.notifySubscribers();\n    }\n    // Update current assistant message (for streaming)\n    updateCurrentAssistantMessage(content) {\n        this.currentAssistantMessage = content;\n        // Find or create current assistant message\n        const lastMessage = this._messages[this._messages.length - 1];\n        if (lastMessage && lastMessage.role === \"assistant\" && lastMessage.id.startsWith(\"temp-\")) {\n            // Update existing temporary message\n            lastMessage.content = content;\n        } else {\n            // Create new temporary message\n            this._messages.push({\n                id: \"temp-assistant-\".concat(this.messageIdCounter++),\n                role: \"assistant\",\n                content,\n                timestamp: Date.now(),\n                type: \"text\"\n            });\n        }\n        this.notifySubscribers();\n    }\n    // Finalize current assistant message\n    finalizeAssistantMessage() {\n        const lastMessage = this._messages[this._messages.length - 1];\n        if (lastMessage && lastMessage.role === \"assistant\" && lastMessage.id.startsWith(\"temp-\")) {\n            // Convert temporary message to permanent\n            lastMessage.id = \"assistant-\".concat(this.messageIdCounter++);\n        }\n        this.currentAssistantMessage = \"\";\n        this.notifySubscribers();\n    }\n    // Handle realtime events\n    handleRealtimeEvent(event) {\n        switch(event.type){\n            case \"conversation.item.input_audio_transcription.completed\":\n                // Add user message\n                this.addMessage({\n                    id: \"user-\".concat(this.messageIdCounter++),\n                    role: \"user\",\n                    content: event.transcript,\n                    timestamp: Date.now(),\n                    type: \"text\"\n                });\n                break;\n            case \"response.audio_transcript.delta\":\n                // Update streaming assistant message\n                this.updateCurrentAssistantMessage(this.currentAssistantMessage + (event.delta || \"\"));\n                break;\n            case \"response.audio_transcript.done\":\n                // Finalize assistant message\n                this.finalizeAssistantMessage();\n                break;\n            case \"response.done\":\n                // Ensure message is finalized\n                this.finalizeAssistantMessage();\n                break;\n        }\n    }\n    // Utility methods\n    cancel() {\n        var // Cancel current response\n        _this_wsRef_current;\n        (_this_wsRef_current = this.wsRef.current) === null || _this_wsRef_current === void 0 ? void 0 : _this_wsRef_current.cancelResponse();\n    }\n    get isRunning() {\n        var _this_wsRef_current;\n        return ((_this_wsRef_current = this.wsRef.current) === null || _this_wsRef_current === void 0 ? void 0 : _this_wsRef_current.getConnectionStatus()) || false;\n    }\n    // Clear conversation\n    clearMessages() {\n        this._messages = [];\n        this.currentAssistantMessage = \"\";\n        this.notifySubscribers();\n    }\n    // Get message count\n    get messageCount() {\n        return this._messages.length;\n    }\n    constructor(wsRef){\n        this._messages = [];\n        this.subscribers = new Set();\n        this.messageIdCounter = 0;\n        this.currentAssistantMessage = \"\";\n        this.wsRef = wsRef;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/realtime-runtime-adapter.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/realtime-websocket.ts":
/*!***********************************!*\
  !*** ./lib/realtime-websocket.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeWebSocket: () => (/* binding */ RealtimeWebSocket)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ RealtimeWebSocket auto */ class RealtimeWebSocket {\n    async connect() {\n        try {\n            // Get ephemeral key\n            const response = await fetch(\"/api/realtime/ephemeral\", {\n                method: \"POST\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get ephemeral key: \".concat(await response.text()));\n            }\n            const { ephemeralKey } = await response.json();\n            // Connect to WebSocket\n            const wsUrl = \"wss://api.openai.com/v1/realtime?model=\".concat(this.config.model || \"gpt-4o-realtime-preview-2024-10-01\");\n            this.ws = new WebSocket(wsUrl, [\n                \"realtime\",\n                \"Bearer.\".concat(ephemeralKey),\n                \"openai-beta.realtime-v1\"\n            ]);\n            this.ws.onopen = ()=>{\n                var _this_config_onConnect, _this_config;\n                this.isConnected = true;\n                this.sessionStartTime = Date.now();\n                this.startSessionTimeout();\n                this.initializeSession();\n                (_this_config_onConnect = (_this_config = this.config).onConnect) === null || _this_config_onConnect === void 0 ? void 0 : _this_config_onConnect.call(_this_config);\n            };\n            this.ws.onmessage = (event)=>{\n                try {\n                    const data = JSON.parse(event.data);\n                    this.handleEvent(data);\n                } catch (error) {\n                    console.error(\"Failed to parse WebSocket message:\", error);\n                }\n            };\n            this.ws.onclose = ()=>{\n                var _this_config_onDisconnect, _this_config;\n                this.isConnected = false;\n                this.clearSessionTimeout();\n                (_this_config_onDisconnect = (_this_config = this.config).onDisconnect) === null || _this_config_onDisconnect === void 0 ? void 0 : _this_config_onDisconnect.call(_this_config);\n            };\n            this.ws.onerror = (error)=>{\n                var _this_config_onError, _this_config;\n                (_this_config_onError = (_this_config = this.config).onError) === null || _this_config_onError === void 0 ? void 0 : _this_config_onError.call(_this_config, new Error(\"WebSocket error\"));\n            };\n        } catch (error) {\n            var _this_config_onError, _this_config;\n            (_this_config_onError = (_this_config = this.config).onError) === null || _this_config_onError === void 0 ? void 0 : _this_config_onError.call(_this_config, error);\n        }\n    }\n    initializeSession() {\n        this.sendEvent({\n            type: \"session.update\",\n            session: {\n                modalities: [\n                    \"text\",\n                    \"audio\"\n                ],\n                voice: this.config.voice || \"alloy\",\n                instructions: this.config.instructions || \"You are a helpful assistant.\",\n                turn_detection: null,\n                input_audio_transcription: {\n                    model: \"whisper-1\"\n                },\n                tools: []\n            }\n        });\n    }\n    handleEvent(event) {\n        var _this_config_onEvent, _this_config;\n        (_this_config_onEvent = (_this_config = this.config).onEvent) === null || _this_config_onEvent === void 0 ? void 0 : _this_config_onEvent.call(_this_config, event);\n    }\n    sendEvent(event) {\n        if (this.ws && this.isConnected) {\n            this.ws.send(JSON.stringify(event));\n        }\n    }\n    // Audio input methods\n    appendAudioBuffer(audioData) {\n        this.sendEvent({\n            type: \"input_audio_buffer.append\",\n            audio: audioData\n        });\n    }\n    commitAudioBuffer() {\n        this.sendEvent({\n            type: \"input_audio_buffer.commit\"\n        });\n    }\n    clearAudioBuffer() {\n        this.sendEvent({\n            type: \"input_audio_buffer.clear\"\n        });\n    }\n    // Response control\n    createResponse() {\n        this.sendEvent({\n            type: \"response.create\"\n        });\n    }\n    cancelResponse() {\n        this.sendEvent({\n            type: \"response.cancel\"\n        });\n    }\n    // Session management\n    startSessionTimeout() {\n        this.timeoutId = setTimeout(()=>{\n            this.reconnect();\n        }, this.SESSION_TIMEOUT);\n    }\n    clearSessionTimeout() {\n        if (this.timeoutId) {\n            clearTimeout(this.timeoutId);\n            this.timeoutId = null;\n        }\n    }\n    async reconnect() {\n        this.disconnect();\n        await new Promise((resolve)=>setTimeout(resolve, 1000)); // Wait 1 second\n        await this.connect();\n    }\n    disconnect() {\n        this.clearSessionTimeout();\n        if (this.ws) {\n            this.ws.close();\n            this.ws = null;\n        }\n        this.isConnected = false;\n    }\n    getConnectionStatus() {\n        return this.isConnected;\n    }\n    getSessionDuration() {\n        return this.sessionStartTime ? Date.now() - this.sessionStartTime : 0;\n    }\n    constructor(config){\n        this.ws = null;\n        this.isConnected = false;\n        this.sessionStartTime = 0;\n        this.SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes\n        this.timeoutId = null;\n        this.config = config;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9yZWFsdGltZS13ZWJzb2NrZXQudHMiLCJtYXBwaW5ncyI6Ijs7Ozt1RUFrQk8sTUFBTUE7SUFZWCxNQUFNQyxVQUF5QjtRQUM3QixJQUFJO1lBQ0Ysb0JBQW9CO1lBQ3BCLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSwyQkFBMkI7Z0JBQ3REQyxRQUFRO1lBQ1Y7WUFFQSxJQUFJLENBQUNGLFNBQVNHLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNLGdDQUFzRCxPQUF0QixNQUFNSixTQUFTSyxJQUFJO1lBQ3JFO1lBRUEsTUFBTSxFQUFFQyxZQUFZLEVBQUUsR0FBRyxNQUFNTixTQUFTTyxJQUFJO1lBRTVDLHVCQUF1QjtZQUN2QixNQUFNQyxRQUFRLDBDQUFvRyxPQUExRCxJQUFJLENBQUNDLE1BQU0sQ0FBQ0MsS0FBSyxJQUFJO1lBQzdFLElBQUksQ0FBQ0MsRUFBRSxHQUFHLElBQUlDLFVBQVVKLE9BQU87Z0JBQzdCO2dCQUNDLFVBQXNCLE9BQWJGO2dCQUNWO2FBQ0Q7WUFFRCxJQUFJLENBQUNLLEVBQUUsQ0FBQ0UsTUFBTSxHQUFHO29CQUtmO2dCQUpBLElBQUksQ0FBQ0MsV0FBVyxHQUFHO2dCQUNuQixJQUFJLENBQUNDLGdCQUFnQixHQUFHQyxLQUFLQyxHQUFHO2dCQUNoQyxJQUFJLENBQUNDLG1CQUFtQjtnQkFDeEIsSUFBSSxDQUFDQyxpQkFBaUI7aUJBQ3RCLDZDQUFJLENBQUNWLE1BQU0sRUFBQ1csU0FBUyxjQUFyQjtZQUNGO1lBRUEsSUFBSSxDQUFDVCxFQUFFLENBQUNVLFNBQVMsR0FBRyxDQUFDQztnQkFDbkIsSUFBSTtvQkFDRixNQUFNQyxPQUFPQyxLQUFLQyxLQUFLLENBQUNILE1BQU1DLElBQUk7b0JBQ2xDLElBQUksQ0FBQ0csV0FBVyxDQUFDSDtnQkFDbkIsRUFBRSxPQUFPSSxPQUFPO29CQUNkQyxRQUFRRCxLQUFLLENBQUMsc0NBQXNDQTtnQkFDdEQ7WUFDRjtZQUVBLElBQUksQ0FBQ2hCLEVBQUUsQ0FBQ2tCLE9BQU8sR0FBRztvQkFHaEI7Z0JBRkEsSUFBSSxDQUFDZixXQUFXLEdBQUc7Z0JBQ25CLElBQUksQ0FBQ2dCLG1CQUFtQjtpQkFDeEIsZ0RBQUksQ0FBQ3JCLE1BQU0sRUFBQ3NCLFlBQVksY0FBeEI7WUFDRjtZQUVBLElBQUksQ0FBQ3BCLEVBQUUsQ0FBQ3FCLE9BQU8sR0FBRyxDQUFDTDtvQkFDakI7aUJBQUEsMkNBQUksQ0FBQ2xCLE1BQU0sRUFBQ3dCLE9BQU8sY0FBbkIsbUZBQXNCLElBQUk3QixNQUFNO1lBQ2xDO1FBRUYsRUFBRSxPQUFPdUIsT0FBTztnQkFDZDthQUFBLDJDQUFJLENBQUNsQixNQUFNLEVBQUN3QixPQUFPLGNBQW5CLG1GQUFzQk47UUFDeEI7SUFDRjtJQUVRUixvQkFBMEI7UUFDaEMsSUFBSSxDQUFDZSxTQUFTLENBQUM7WUFDYkMsTUFBTTtZQUNOQyxTQUFTO2dCQUNQQyxZQUFZO29CQUFDO29CQUFRO2lCQUFRO2dCQUM3QkMsT0FBTyxJQUFJLENBQUM3QixNQUFNLENBQUM2QixLQUFLLElBQUk7Z0JBQzVCQyxjQUFjLElBQUksQ0FBQzlCLE1BQU0sQ0FBQzhCLFlBQVksSUFBSTtnQkFDMUNDLGdCQUFnQjtnQkFDaEJDLDJCQUEyQjtvQkFDekIvQixPQUFPO2dCQUNUO2dCQUNBZ0MsT0FBTyxFQUFFO1lBQ1g7UUFDRjtJQUNGO0lBRVFoQixZQUFZSixLQUFvQixFQUFRO1lBQzlDO1NBQUEsMkNBQUksQ0FBQ2IsTUFBTSxFQUFDa0MsT0FBTyxjQUFuQixtRkFBc0JyQjtJQUN4QjtJQUVBWSxVQUFVWixLQUFvQixFQUFRO1FBQ3BDLElBQUksSUFBSSxDQUFDWCxFQUFFLElBQUksSUFBSSxDQUFDRyxXQUFXLEVBQUU7WUFDL0IsSUFBSSxDQUFDSCxFQUFFLENBQUNpQyxJQUFJLENBQUNwQixLQUFLcUIsU0FBUyxDQUFDdkI7UUFDOUI7SUFDRjtJQUVBLHNCQUFzQjtJQUN0QndCLGtCQUFrQkMsU0FBaUIsRUFBUTtRQUN6QyxJQUFJLENBQUNiLFNBQVMsQ0FBQztZQUNiQyxNQUFNO1lBQ05hLE9BQU9EO1FBQ1Q7SUFDRjtJQUVBRSxvQkFBMEI7UUFDeEIsSUFBSSxDQUFDZixTQUFTLENBQUM7WUFDYkMsTUFBTTtRQUNSO0lBQ0Y7SUFFQWUsbUJBQXlCO1FBQ3ZCLElBQUksQ0FBQ2hCLFNBQVMsQ0FBQztZQUNiQyxNQUFNO1FBQ1I7SUFDRjtJQUVBLG1CQUFtQjtJQUNuQmdCLGlCQUF1QjtRQUNyQixJQUFJLENBQUNqQixTQUFTLENBQUM7WUFDYkMsTUFBTTtRQUNSO0lBQ0Y7SUFFQWlCLGlCQUF1QjtRQUNyQixJQUFJLENBQUNsQixTQUFTLENBQUM7WUFDYkMsTUFBTTtRQUNSO0lBQ0Y7SUFFQSxxQkFBcUI7SUFDYmpCLHNCQUE0QjtRQUNsQyxJQUFJLENBQUNtQyxTQUFTLEdBQUdDLFdBQVc7WUFDMUIsSUFBSSxDQUFDQyxTQUFTO1FBQ2hCLEdBQUcsSUFBSSxDQUFDQyxlQUFlO0lBQ3pCO0lBRVExQixzQkFBNEI7UUFDbEMsSUFBSSxJQUFJLENBQUN1QixTQUFTLEVBQUU7WUFDbEJJLGFBQWEsSUFBSSxDQUFDSixTQUFTO1lBQzNCLElBQUksQ0FBQ0EsU0FBUyxHQUFHO1FBQ25CO0lBQ0Y7SUFFQSxNQUFjRSxZQUEyQjtRQUN2QyxJQUFJLENBQUNHLFVBQVU7UUFDZixNQUFNLElBQUlDLFFBQVFDLENBQUFBLFVBQVdOLFdBQVdNLFNBQVMsUUFBUSxnQkFBZ0I7UUFDekUsTUFBTSxJQUFJLENBQUM3RCxPQUFPO0lBQ3BCO0lBRUEyRCxhQUFtQjtRQUNqQixJQUFJLENBQUM1QixtQkFBbUI7UUFDeEIsSUFBSSxJQUFJLENBQUNuQixFQUFFLEVBQUU7WUFDWCxJQUFJLENBQUNBLEVBQUUsQ0FBQ2tELEtBQUs7WUFDYixJQUFJLENBQUNsRCxFQUFFLEdBQUc7UUFDWjtRQUNBLElBQUksQ0FBQ0csV0FBVyxHQUFHO0lBQ3JCO0lBRUFnRCxzQkFBK0I7UUFDN0IsT0FBTyxJQUFJLENBQUNoRCxXQUFXO0lBQ3pCO0lBRUFpRCxxQkFBNkI7UUFDM0IsT0FBTyxJQUFJLENBQUNoRCxnQkFBZ0IsR0FBR0MsS0FBS0MsR0FBRyxLQUFLLElBQUksQ0FBQ0YsZ0JBQWdCLEdBQUc7SUFDdEU7SUF2SkEsWUFBWU4sTUFBc0IsQ0FBRTthQVA1QkUsS0FBdUI7YUFFdkJHLGNBQWM7YUFDZEMsbUJBQTJCO2FBQ2xCeUMsa0JBQWtCLEtBQUssS0FBSyxNQUFNLGFBQWE7YUFDeERILFlBQW1DO1FBR3pDLElBQUksQ0FBQzVDLE1BQU0sR0FBR0E7SUFDaEI7QUFzSkYiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYXRoaS93b3JrL0luc3RydW1lbnQtMi9zeXNfZGVzaWduX2luc3RydW1lbnQvbGliL3JlYWx0aW1lLXdlYnNvY2tldC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuZXhwb3J0IGludGVyZmFjZSBSZWFsdGltZUV2ZW50IHtcbiAgdHlwZTogc3RyaW5nO1xuICBba2V5OiBzdHJpbmddOiBhbnk7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUmVhbHRpbWVDb25maWcge1xuICBhcGlLZXk6IHN0cmluZztcbiAgbW9kZWw/OiBzdHJpbmc7XG4gIHZvaWNlPzogc3RyaW5nO1xuICBpbnN0cnVjdGlvbnM/OiBzdHJpbmc7XG4gIG9uRXZlbnQ/OiAoZXZlbnQ6IFJlYWx0aW1lRXZlbnQpID0+IHZvaWQ7XG4gIG9uRXJyb3I/OiAoZXJyb3I6IEVycm9yKSA9PiB2b2lkO1xuICBvbkNvbm5lY3Q/OiAoKSA9PiB2b2lkO1xuICBvbkRpc2Nvbm5lY3Q/OiAoKSA9PiB2b2lkO1xufVxuXG5leHBvcnQgY2xhc3MgUmVhbHRpbWVXZWJTb2NrZXQge1xuICBwcml2YXRlIHdzOiBXZWJTb2NrZXQgfCBudWxsID0gbnVsbDtcbiAgcHJpdmF0ZSBjb25maWc6IFJlYWx0aW1lQ29uZmlnO1xuICBwcml2YXRlIGlzQ29ubmVjdGVkID0gZmFsc2U7XG4gIHByaXZhdGUgc2Vzc2lvblN0YXJ0VGltZTogbnVtYmVyID0gMDtcbiAgcHJpdmF0ZSByZWFkb25seSBTRVNTSU9OX1RJTUVPVVQgPSAzMCAqIDYwICogMTAwMDsgLy8gMzAgbWludXRlc1xuICBwcml2YXRlIHRpbWVvdXRJZDogTm9kZUpTLlRpbWVvdXQgfCBudWxsID0gbnVsbDtcblxuICBjb25zdHJ1Y3Rvcihjb25maWc6IFJlYWx0aW1lQ29uZmlnKSB7XG4gICAgdGhpcy5jb25maWcgPSBjb25maWc7XG4gIH1cblxuICBhc3luYyBjb25uZWN0KCk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBHZXQgZXBoZW1lcmFsIGtleVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChcIi9hcGkvcmVhbHRpbWUvZXBoZW1lcmFsXCIsIHtcbiAgICAgICAgbWV0aG9kOiBcIlBPU1RcIixcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGdldCBlcGhlbWVyYWwga2V5OiAke2F3YWl0IHJlc3BvbnNlLnRleHQoKX1gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgeyBlcGhlbWVyYWxLZXkgfSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgICAgLy8gQ29ubmVjdCB0byBXZWJTb2NrZXRcbiAgICAgIGNvbnN0IHdzVXJsID0gYHdzczovL2FwaS5vcGVuYWkuY29tL3YxL3JlYWx0aW1lP21vZGVsPSR7dGhpcy5jb25maWcubW9kZWwgfHwgXCJncHQtNG8tcmVhbHRpbWUtcHJldmlldy0yMDI0LTEwLTAxXCJ9YDtcbiAgICAgIHRoaXMud3MgPSBuZXcgV2ViU29ja2V0KHdzVXJsLCBbXG4gICAgICAgIFwicmVhbHRpbWVcIixcbiAgICAgICAgYEJlYXJlci4ke2VwaGVtZXJhbEtleX1gLFxuICAgICAgICBcIm9wZW5haS1iZXRhLnJlYWx0aW1lLXYxXCJcbiAgICAgIF0pO1xuXG4gICAgICB0aGlzLndzLm9ub3BlbiA9ICgpID0+IHtcbiAgICAgICAgdGhpcy5pc0Nvbm5lY3RlZCA9IHRydWU7XG4gICAgICAgIHRoaXMuc2Vzc2lvblN0YXJ0VGltZSA9IERhdGUubm93KCk7XG4gICAgICAgIHRoaXMuc3RhcnRTZXNzaW9uVGltZW91dCgpO1xuICAgICAgICB0aGlzLmluaXRpYWxpemVTZXNzaW9uKCk7XG4gICAgICAgIHRoaXMuY29uZmlnLm9uQ29ubmVjdD8uKCk7XG4gICAgICB9O1xuXG4gICAgICB0aGlzLndzLm9ubWVzc2FnZSA9IChldmVudCkgPT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IGRhdGEgPSBKU09OLnBhcnNlKGV2ZW50LmRhdGEpO1xuICAgICAgICAgIHRoaXMuaGFuZGxlRXZlbnQoZGF0YSk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBwYXJzZSBXZWJTb2NrZXQgbWVzc2FnZTpcIiwgZXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9O1xuXG4gICAgICB0aGlzLndzLm9uY2xvc2UgPSAoKSA9PiB7XG4gICAgICAgIHRoaXMuaXNDb25uZWN0ZWQgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5jbGVhclNlc3Npb25UaW1lb3V0KCk7XG4gICAgICAgIHRoaXMuY29uZmlnLm9uRGlzY29ubmVjdD8uKCk7XG4gICAgICB9O1xuXG4gICAgICB0aGlzLndzLm9uZXJyb3IgPSAoZXJyb3IpID0+IHtcbiAgICAgICAgdGhpcy5jb25maWcub25FcnJvcj8uKG5ldyBFcnJvcihcIldlYlNvY2tldCBlcnJvclwiKSk7XG4gICAgICB9O1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRoaXMuY29uZmlnLm9uRXJyb3I/LihlcnJvciBhcyBFcnJvcik7XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBpbml0aWFsaXplU2Vzc2lvbigpOiB2b2lkIHtcbiAgICB0aGlzLnNlbmRFdmVudCh7XG4gICAgICB0eXBlOiBcInNlc3Npb24udXBkYXRlXCIsXG4gICAgICBzZXNzaW9uOiB7XG4gICAgICAgIG1vZGFsaXRpZXM6IFtcInRleHRcIiwgXCJhdWRpb1wiXSxcbiAgICAgICAgdm9pY2U6IHRoaXMuY29uZmlnLnZvaWNlIHx8IFwiYWxsb3lcIixcbiAgICAgICAgaW5zdHJ1Y3Rpb25zOiB0aGlzLmNvbmZpZy5pbnN0cnVjdGlvbnMgfHwgXCJZb3UgYXJlIGEgaGVscGZ1bCBhc3Npc3RhbnQuXCIsXG4gICAgICAgIHR1cm5fZGV0ZWN0aW9uOiBudWxsLCAvLyBEaXNhYmxlIHNlcnZlciBWQUQgZm9yIG1hbnVhbCBjb250cm9sXG4gICAgICAgIGlucHV0X2F1ZGlvX3RyYW5zY3JpcHRpb246IHtcbiAgICAgICAgICBtb2RlbDogXCJ3aGlzcGVyLTFcIlxuICAgICAgICB9LFxuICAgICAgICB0b29sczogW11cbiAgICAgIH1cbiAgICB9KTtcbiAgfVxuXG4gIHByaXZhdGUgaGFuZGxlRXZlbnQoZXZlbnQ6IFJlYWx0aW1lRXZlbnQpOiB2b2lkIHtcbiAgICB0aGlzLmNvbmZpZy5vbkV2ZW50Py4oZXZlbnQpO1xuICB9XG5cbiAgc2VuZEV2ZW50KGV2ZW50OiBSZWFsdGltZUV2ZW50KTogdm9pZCB7XG4gICAgaWYgKHRoaXMud3MgJiYgdGhpcy5pc0Nvbm5lY3RlZCkge1xuICAgICAgdGhpcy53cy5zZW5kKEpTT04uc3RyaW5naWZ5KGV2ZW50KSk7XG4gICAgfVxuICB9XG5cbiAgLy8gQXVkaW8gaW5wdXQgbWV0aG9kc1xuICBhcHBlbmRBdWRpb0J1ZmZlcihhdWRpb0RhdGE6IHN0cmluZyk6IHZvaWQge1xuICAgIHRoaXMuc2VuZEV2ZW50KHtcbiAgICAgIHR5cGU6IFwiaW5wdXRfYXVkaW9fYnVmZmVyLmFwcGVuZFwiLFxuICAgICAgYXVkaW86IGF1ZGlvRGF0YVxuICAgIH0pO1xuICB9XG5cbiAgY29tbWl0QXVkaW9CdWZmZXIoKTogdm9pZCB7XG4gICAgdGhpcy5zZW5kRXZlbnQoe1xuICAgICAgdHlwZTogXCJpbnB1dF9hdWRpb19idWZmZXIuY29tbWl0XCJcbiAgICB9KTtcbiAgfVxuXG4gIGNsZWFyQXVkaW9CdWZmZXIoKTogdm9pZCB7XG4gICAgdGhpcy5zZW5kRXZlbnQoe1xuICAgICAgdHlwZTogXCJpbnB1dF9hdWRpb19idWZmZXIuY2xlYXJcIlxuICAgIH0pO1xuICB9XG5cbiAgLy8gUmVzcG9uc2UgY29udHJvbFxuICBjcmVhdGVSZXNwb25zZSgpOiB2b2lkIHtcbiAgICB0aGlzLnNlbmRFdmVudCh7XG4gICAgICB0eXBlOiBcInJlc3BvbnNlLmNyZWF0ZVwiXG4gICAgfSk7XG4gIH1cblxuICBjYW5jZWxSZXNwb25zZSgpOiB2b2lkIHtcbiAgICB0aGlzLnNlbmRFdmVudCh7XG4gICAgICB0eXBlOiBcInJlc3BvbnNlLmNhbmNlbFwiXG4gICAgfSk7XG4gIH1cblxuICAvLyBTZXNzaW9uIG1hbmFnZW1lbnRcbiAgcHJpdmF0ZSBzdGFydFNlc3Npb25UaW1lb3V0KCk6IHZvaWQge1xuICAgIHRoaXMudGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICB0aGlzLnJlY29ubmVjdCgpO1xuICAgIH0sIHRoaXMuU0VTU0lPTl9USU1FT1VUKTtcbiAgfVxuXG4gIHByaXZhdGUgY2xlYXJTZXNzaW9uVGltZW91dCgpOiB2b2lkIHtcbiAgICBpZiAodGhpcy50aW1lb3V0SWQpIHtcbiAgICAgIGNsZWFyVGltZW91dCh0aGlzLnRpbWVvdXRJZCk7XG4gICAgICB0aGlzLnRpbWVvdXRJZCA9IG51bGw7XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBhc3luYyByZWNvbm5lY3QoKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgdGhpcy5kaXNjb25uZWN0KCk7XG4gICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMDApKTsgLy8gV2FpdCAxIHNlY29uZFxuICAgIGF3YWl0IHRoaXMuY29ubmVjdCgpO1xuICB9XG5cbiAgZGlzY29ubmVjdCgpOiB2b2lkIHtcbiAgICB0aGlzLmNsZWFyU2Vzc2lvblRpbWVvdXQoKTtcbiAgICBpZiAodGhpcy53cykge1xuICAgICAgdGhpcy53cy5jbG9zZSgpO1xuICAgICAgdGhpcy53cyA9IG51bGw7XG4gICAgfVxuICAgIHRoaXMuaXNDb25uZWN0ZWQgPSBmYWxzZTtcbiAgfVxuXG4gIGdldENvbm5lY3Rpb25TdGF0dXMoKTogYm9vbGVhbiB7XG4gICAgcmV0dXJuIHRoaXMuaXNDb25uZWN0ZWQ7XG4gIH1cblxuICBnZXRTZXNzaW9uRHVyYXRpb24oKTogbnVtYmVyIHtcbiAgICByZXR1cm4gdGhpcy5zZXNzaW9uU3RhcnRUaW1lID8gRGF0ZS5ub3coKSAtIHRoaXMuc2Vzc2lvblN0YXJ0VGltZSA6IDA7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJSZWFsdGltZVdlYlNvY2tldCIsImNvbm5lY3QiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwib2siLCJFcnJvciIsInRleHQiLCJlcGhlbWVyYWxLZXkiLCJqc29uIiwid3NVcmwiLCJjb25maWciLCJtb2RlbCIsIndzIiwiV2ViU29ja2V0Iiwib25vcGVuIiwiaXNDb25uZWN0ZWQiLCJzZXNzaW9uU3RhcnRUaW1lIiwiRGF0ZSIsIm5vdyIsInN0YXJ0U2Vzc2lvblRpbWVvdXQiLCJpbml0aWFsaXplU2Vzc2lvbiIsIm9uQ29ubmVjdCIsIm9ubWVzc2FnZSIsImV2ZW50IiwiZGF0YSIsIkpTT04iLCJwYXJzZSIsImhhbmRsZUV2ZW50IiwiZXJyb3IiLCJjb25zb2xlIiwib25jbG9zZSIsImNsZWFyU2Vzc2lvblRpbWVvdXQiLCJvbkRpc2Nvbm5lY3QiLCJvbmVycm9yIiwib25FcnJvciIsInNlbmRFdmVudCIsInR5cGUiLCJzZXNzaW9uIiwibW9kYWxpdGllcyIsInZvaWNlIiwiaW5zdHJ1Y3Rpb25zIiwidHVybl9kZXRlY3Rpb24iLCJpbnB1dF9hdWRpb190cmFuc2NyaXB0aW9uIiwidG9vbHMiLCJvbkV2ZW50Iiwic2VuZCIsInN0cmluZ2lmeSIsImFwcGVuZEF1ZGlvQnVmZmVyIiwiYXVkaW9EYXRhIiwiYXVkaW8iLCJjb21taXRBdWRpb0J1ZmZlciIsImNsZWFyQXVkaW9CdWZmZXIiLCJjcmVhdGVSZXNwb25zZSIsImNhbmNlbFJlc3BvbnNlIiwidGltZW91dElkIiwic2V0VGltZW91dCIsInJlY29ubmVjdCIsIlNFU1NJT05fVElNRU9VVCIsImNsZWFyVGltZW91dCIsImRpc2Nvbm5lY3QiLCJQcm9taXNlIiwicmVzb2x2ZSIsImNsb3NlIiwiZ2V0Q29ubmVjdGlvblN0YXR1cyIsImdldFNlc3Npb25EdXJhdGlvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/realtime-websocket.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js":
/*!****************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/Icon.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Icon)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultAttributes.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js\");\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst Icon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(_c = (param, ref)=>{\n    let { color = \"currentColor\", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = \"\", children, iconNode, ...rest } = param;\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", {\n        ref,\n        ..._defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n        className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide\", className),\n        ...!children && !(0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.hasA11yProp)(rest) && {\n            \"aria-hidden\": \"true\"\n        },\n        ...rest\n    }, [\n        ...iconNode.map((param)=>{\n            let [tag, attrs] = param;\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(tag, attrs);\n        }),\n        ...Array.isArray(children) ? children : [\n            children\n        ]\n    ]);\n});\n_c1 = Icon;\n //# sourceMappingURL=Icon.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Icon$forwardRef\");\n$RefreshReg$(_c1, \"Icon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/createLucideIcon.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createLucideIcon)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/* harmony import */ var _Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Icon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js\");\n/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst createLucideIcon = (iconName, iconNode)=>{\n    const Component = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((param, ref)=>{\n        let { className, ...props } = param;\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            ref,\n            iconNode,\n            className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide-\".concat((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toKebabCase)((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toPascalCase)(iconName))), \"lucide-\".concat(iconName), className),\n            ...props\n        });\n    });\n    Component.displayName = (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toPascalCase)(iconName);\n    return Component;\n};\n //# sourceMappingURL=createLucideIcon.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vY3JlYXRlTHVjaWRlSWNvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBV0EsTUFBTSxtQkFBbUIsQ0FBQyxVQUFrQjtJQUMxQyxNQUFNLDBCQUFZLGtEQUF1QyxRQUEwQjtZQUF6QixFQUFFLFdBQVcsR0FBRyxPQUFNOzZCQUM5RSxxREFBYyxrREFBTTtZQUNsQjtZQUNBO1lBQ0EsV0FBVyxtRUFDVCxVQUE2QyxPQUFuQyxrRUFBWSxtRUFBYSxRQUFRLENBQUMsQ0FBQyxHQUM3QyxVQUFrQixPQUFSLFFBQVEsR0FDbEI7WUFFRixHQUFHO1FBQUEsQ0FDSjs7SUFHSCxVQUFVLGNBQWMsbUVBQWEsUUFBUTtJQUU3QyxPQUFPO0FBQ1QiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYXRoaS93b3JrL3NyYy9jcmVhdGVMdWNpZGVJY29uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUVsZW1lbnQsIGZvcndhcmRSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBtZXJnZUNsYXNzZXMsIHRvS2ViYWJDYXNlLCB0b1Bhc2NhbENhc2UgfSBmcm9tICdAbHVjaWRlL3NoYXJlZCc7XG5pbXBvcnQgeyBJY29uTm9kZSwgTHVjaWRlUHJvcHMgfSBmcm9tICcuL3R5cGVzJztcbmltcG9ydCBJY29uIGZyb20gJy4vSWNvbic7XG5cbi8qKlxuICogQ3JlYXRlIGEgTHVjaWRlIGljb24gY29tcG9uZW50XG4gKiBAcGFyYW0ge3N0cmluZ30gaWNvbk5hbWVcbiAqIEBwYXJhbSB7YXJyYXl9IGljb25Ob2RlXG4gKiBAcmV0dXJucyB7Rm9yd2FyZFJlZkV4b3RpY0NvbXBvbmVudH0gTHVjaWRlSWNvblxuICovXG5jb25zdCBjcmVhdGVMdWNpZGVJY29uID0gKGljb25OYW1lOiBzdHJpbmcsIGljb25Ob2RlOiBJY29uTm9kZSkgPT4ge1xuICBjb25zdCBDb21wb25lbnQgPSBmb3J3YXJkUmVmPFNWR1NWR0VsZW1lbnQsIEx1Y2lkZVByb3BzPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT5cbiAgICBjcmVhdGVFbGVtZW50KEljb24sIHtcbiAgICAgIHJlZixcbiAgICAgIGljb25Ob2RlLFxuICAgICAgY2xhc3NOYW1lOiBtZXJnZUNsYXNzZXMoXG4gICAgICAgIGBsdWNpZGUtJHt0b0tlYmFiQ2FzZSh0b1Bhc2NhbENhc2UoaWNvbk5hbWUpKX1gLFxuICAgICAgICBgbHVjaWRlLSR7aWNvbk5hbWV9YCxcbiAgICAgICAgY2xhc3NOYW1lLFxuICAgICAgKSxcbiAgICAgIC4uLnByb3BzLFxuICAgIH0pLFxuICApO1xuXG4gIENvbXBvbmVudC5kaXNwbGF5TmFtZSA9IHRvUGFzY2FsQ2FzZShpY29uTmFtZSk7XG5cbiAgcmV0dXJuIENvbXBvbmVudDtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGNyZWF0ZUx1Y2lkZUljb247XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/defaultAttributes.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultAttributes)\n/* harmony export */ });\n/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ var defaultAttributes = {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: 2,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n};\n //# sourceMappingURL=defaultAttributes.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vZGVmYXVsdEF0dHJpYnV0ZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0lBQUEsd0JBQWU7SUFDYixPQUFPO0lBQ1AsT0FBTztJQUNQLFFBQVE7SUFDUixTQUFTO0lBQ1QsTUFBTTtJQUNOLFFBQVE7SUFDUixhQUFhO0lBQ2IsZUFBZTtJQUNmLGdCQUFnQjtBQUNsQiIsInNvdXJjZXMiOlsiL1VzZXJzL3JhdGhpL3dvcmsvc3JjL2RlZmF1bHRBdHRyaWJ1dGVzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcbiAgeG1sbnM6ICdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycsXG4gIHdpZHRoOiAyNCxcbiAgaGVpZ2h0OiAyNCxcbiAgdmlld0JveDogJzAgMCAyNCAyNCcsXG4gIGZpbGw6ICdub25lJyxcbiAgc3Ryb2tlOiAnY3VycmVudENvbG9yJyxcbiAgc3Ryb2tlV2lkdGg6IDIsXG4gIHN0cm9rZUxpbmVjYXA6ICdyb3VuZCcsXG4gIHN0cm9rZUxpbmVqb2luOiAncm91bmQnLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic-off.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/mic-off.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ MicOff)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 19v3\",\n            key: \"npa21l\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M15 9.34V5a3 3 0 0 0-5.68-1.33\",\n            key: \"1gzdoj\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16.95 16.95A7 7 0 0 1 5 12v-2\",\n            key: \"cqa7eg\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18.89 13.23A7 7 0 0 0 19 12v-2\",\n            key: \"16hl24\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m2 2 20 20\",\n            key: \"1ooewy\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 9v3a3 3 0 0 0 5.12 2.12\",\n            key: \"r2i35w\"\n        }\n    ]\n];\nconst MicOff = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"mic-off\", __iconNode);\n //# sourceMappingURL=mic-off.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/mic.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Mic)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 19v3\",\n            key: \"npa21l\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19 10v2a7 7 0 0 1-14 0v-2\",\n            key: \"1vc78b\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            x: \"9\",\n            y: \"2\",\n            width: \"6\",\n            height: \"13\",\n            rx: \"3\",\n            key: \"s6n7sd\"\n        }\n    ]\n];\nconst Mic = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"mic\", __iconNode);\n //# sourceMappingURL=mic.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/volume-2.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Volume2)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z\",\n            key: \"uqj9uw\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 9a5 5 0 0 1 0 6\",\n            key: \"1q6k2b\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19.364 18.364a9 9 0 0 0 0-12.728\",\n            key: \"ijwkga\"\n        }\n    ]\n];\nconst Volume2 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"volume-2\", __iconNode);\n //# sourceMappingURL=volume-2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/volume-x.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ VolumeX)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z\",\n            key: \"uqj9uw\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"22\",\n            x2: \"16\",\n            y1: \"9\",\n            y2: \"15\",\n            key: \"1ewh16\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"16\",\n            x2: \"22\",\n            y1: \"9\",\n            y2: \"15\",\n            key: \"5ykzw1\"\n        }\n    ]\n];\nconst VolumeX = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"volume-x\", __iconNode);\n //# sourceMappingURL=volume-x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdm9sdW1lLXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR08sTUFBTSxhQUF1QjtJQUNsQztRQUNFO1FBQ0E7WUFDRSxHQUFHO1lBQ0gsS0FBSztRQUFBO0tBRVQ7SUFDQTtRQUFDO1FBQVE7WUFBRSxJQUFJO1lBQU0sSUFBSTtZQUFNLElBQUk7WUFBSyxJQUFJO1lBQU0sS0FBSztRQUFBLENBQVU7S0FBQTtJQUNqRTtRQUFDO1FBQVE7WUFBRSxJQUFJO1lBQU0sSUFBSTtZQUFNLElBQUk7WUFBSyxJQUFJO1lBQU0sS0FBSztRQUFBLENBQVU7S0FBQTtDQUNuRTtBQWFBLE1BQU0sVUFBVSxpRUFBaUIsWUFBWSxVQUFVIiwic291cmNlcyI6WyIvVXNlcnMvcmF0aGkvc3JjL2ljb25zL3ZvbHVtZS14LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtcbiAgW1xuICAgICdwYXRoJyxcbiAgICB7XG4gICAgICBkOiAnTTExIDQuNzAyYS43MDUuNzA1IDAgMCAwLTEuMjAzLS40OThMNi40MTMgNy41ODdBMS40IDEuNCAwIDAgMSA1LjQxNiA4SDNhMSAxIDAgMCAwLTEgMXY2YTEgMSAwIDAgMCAxIDFoMi40MTZhMS40IDEuNCAwIDAgMSAuOTk3LjQxM2wzLjM4MyAzLjM4NEEuNzA1LjcwNSAwIDAgMCAxMSAxOS4yOTh6JyxcbiAgICAgIGtleTogJ3Vxajl1dycsXG4gICAgfSxcbiAgXSxcbiAgWydsaW5lJywgeyB4MTogJzIyJywgeDI6ICcxNicsIHkxOiAnOScsIHkyOiAnMTUnLCBrZXk6ICcxZXdoMTYnIH1dLFxuICBbJ2xpbmUnLCB7IHgxOiAnMTYnLCB4MjogJzIyJywgeTE6ICc5JywgeTI6ICcxNScsIGtleTogJzV5a3p3MScgfV0sXG5dO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgVm9sdW1lWFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTVRFZ05DNDNNREpoTGpjd05TNDNNRFVnTUNBd0lEQXRNUzR5TURNdExqUTVPRXcyTGpReE15QTNMalU0TjBFeExqUWdNUzQwSURBZ01DQXhJRFV1TkRFMklEaElNMkV4SURFZ01DQXdJREF0TVNBeGRqWmhNU0F4SURBZ01DQXdJREVnTVdneUxqUXhObUV4TGpRZ01TNDBJREFnTUNBeElDNDVPVGN1TkRFemJETXVNemd6SURNdU16ZzBRUzQzTURVdU56QTFJREFnTUNBd0lERXhJREU1TGpJNU9Ib2lJQzgrQ2lBZ1BHeHBibVVnZURFOUlqSXlJaUI0TWowaU1UWWlJSGt4UFNJNUlpQjVNajBpTVRVaUlDOCtDaUFnUEd4cGJtVWdlREU5SWpFMklpQjRNajBpTWpJaUlIa3hQU0k1SWlCNU1qMGlNVFVpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvdm9sdW1lLXhcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBWb2x1bWVYID0gY3JlYXRlTHVjaWRlSWNvbigndm9sdW1lLXgnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgVm9sdW1lWDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/shared/src/utils.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasA11yProp: () => (/* binding */ hasA11yProp),\n/* harmony export */   mergeClasses: () => (/* binding */ mergeClasses),\n/* harmony export */   toCamelCase: () => (/* binding */ toCamelCase),\n/* harmony export */   toKebabCase: () => (/* binding */ toKebabCase),\n/* harmony export */   toPascalCase: () => (/* binding */ toPascalCase)\n/* harmony export */ });\n/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ const toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string)=>string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2)=>p2 ? p2.toUpperCase() : p1.toLowerCase());\nconst toPascalCase = (string)=>{\n    const camelCase = toCamelCase(string);\n    return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = function() {\n    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){\n        classes[_key] = arguments[_key];\n    }\n    return classes.filter((className, index, array)=>{\n        return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n    }).join(\" \").trim();\n};\nconst hasA11yProp = (props)=>{\n    for(const prop in props){\n        if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n            return true;\n        }\n    }\n};\n //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vc2hhcmVkL3NyYy91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0lBUU8sTUFBTSxjQUFjLENBQUMsU0FDMUIsT0FBTyxRQUFRLHNCQUFzQixPQUFPLEVBQUU7QUFRekMsTUFBTSxjQUFjLENBQW1CLFNBQzVDLENBRDRDLENBQzVDLEtBQU8sUUFBUSx5QkFBeUIsQ0FBQyxPQUFPLElBQUksVUFDN0MsR0FBRyxhQUFZLEdBQUksR0FBRztBQVN4QixNQUFNLGVBQWUsQ0FBbUI7SUFDN0MsTUFBTSxZQUFZLFlBQVksTUFBTTtJQUVwQyxPQUFRLFVBQVUsT0FBTyxDQUFDLEVBQUUsYUFBWSxHQUFJLFVBQVUsTUFBTSxDQUFDO0FBQy9EO0FBUU8sTUFBTSxlQUFlOztRQUEyQzs7V0FDckUsUUFDRyxPQUFPLENBQUMsV0FBVyxPQUFPO1FBQ3pCLE9BQ0UsUUFBUSxTQUFTLEtBQ2hCLFVBQXFCLFdBQVcsTUFDakMsTUFBTSxRQUFRLFNBQVMsTUFBTTtJQUVqQyxDQUFDLEVBQ0EsS0FBSyxHQUFHLEVBQ1I7O0FBZ0JFLE1BQU0sY0FBYyxDQUFDO0lBQzFCLFVBQVcsUUFBUTtRQUNqQixJQUFJLEtBQUssV0FBVyxPQUFPLEtBQUssU0FBUyxVQUFVLFNBQVMsU0FBUztZQUNuRSxPQUFPO1FBQ1Q7SUFDRjtBQUNGIiwic291cmNlcyI6WyIvc2hhcmVkL3NyYy91dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDYW1lbFRvUGFzY2FsIH0gZnJvbSAnLi91dGlsaXR5LXR5cGVzJztcblxuLyoqXG4gKiBDb252ZXJ0cyBzdHJpbmcgdG8ga2ViYWIgY2FzZVxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBzdHJpbmdcbiAqIEByZXR1cm5zIHtzdHJpbmd9IEEga2ViYWJpemVkIHN0cmluZ1xuICovXG5leHBvcnQgY29uc3QgdG9LZWJhYkNhc2UgPSAoc3RyaW5nOiBzdHJpbmcpID0+XG4gIHN0cmluZy5yZXBsYWNlKC8oW2EtejAtOV0pKFtBLVpdKS9nLCAnJDEtJDInKS50b0xvd2VyQ2FzZSgpO1xuXG4vKipcbiAqIENvbnZlcnRzIHN0cmluZyB0byBjYW1lbCBjYXNlXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IHN0cmluZ1xuICogQHJldHVybnMge3N0cmluZ30gQSBjYW1lbGl6ZWQgc3RyaW5nXG4gKi9cbmV4cG9ydCBjb25zdCB0b0NhbWVsQ2FzZSA9IDxUIGV4dGVuZHMgc3RyaW5nPihzdHJpbmc6IFQpID0+XG4gIHN0cmluZy5yZXBsYWNlKC9eKFtBLVpdKXxbXFxzLV9dKyhcXHcpL2csIChtYXRjaCwgcDEsIHAyKSA9PlxuICAgIHAyID8gcDIudG9VcHBlckNhc2UoKSA6IHAxLnRvTG93ZXJDYXNlKCksXG4gICk7XG5cbi8qKlxuICogQ29udmVydHMgc3RyaW5nIHRvIHBhc2NhbCBjYXNlXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IHN0cmluZ1xuICogQHJldHVybnMge3N0cmluZ30gQSBwYXNjYWxpemVkIHN0cmluZ1xuICovXG5leHBvcnQgY29uc3QgdG9QYXNjYWxDYXNlID0gPFQgZXh0ZW5kcyBzdHJpbmc+KHN0cmluZzogVCk6IENhbWVsVG9QYXNjYWw8VD4gPT4ge1xuICBjb25zdCBjYW1lbENhc2UgPSB0b0NhbWVsQ2FzZShzdHJpbmcpO1xuXG4gIHJldHVybiAoY2FtZWxDYXNlLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgY2FtZWxDYXNlLnNsaWNlKDEpKSBhcyBDYW1lbFRvUGFzY2FsPFQ+O1xufTtcblxuLyoqXG4gKiBNZXJnZXMgY2xhc3NlcyBpbnRvIGEgc2luZ2xlIHN0cmluZ1xuICpcbiAqIEBwYXJhbSB7YXJyYXl9IGNsYXNzZXNcbiAqIEByZXR1cm5zIHtzdHJpbmd9IEEgc3RyaW5nIG9mIGNsYXNzZXNcbiAqL1xuZXhwb3J0IGNvbnN0IG1lcmdlQ2xhc3NlcyA9IDxDbGFzc1R5cGUgPSBzdHJpbmcgfCB1bmRlZmluZWQgfCBudWxsPiguLi5jbGFzc2VzOiBDbGFzc1R5cGVbXSkgPT5cbiAgY2xhc3Nlc1xuICAgIC5maWx0ZXIoKGNsYXNzTmFtZSwgaW5kZXgsIGFycmF5KSA9PiB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICBCb29sZWFuKGNsYXNzTmFtZSkgJiZcbiAgICAgICAgKGNsYXNzTmFtZSBhcyBzdHJpbmcpLnRyaW0oKSAhPT0gJycgJiZcbiAgICAgICAgYXJyYXkuaW5kZXhPZihjbGFzc05hbWUpID09PSBpbmRleFxuICAgICAgKTtcbiAgICB9KVxuICAgIC5qb2luKCcgJylcbiAgICAudHJpbSgpO1xuXG4vKipcbiAqIElzIGVtcHR5IHN0cmluZ1xuICpcbiAqIEBwYXJhbSB7dW5rbm93bn0gdmFsdWVcbiAqIEByZXR1cm5zIHtib29sZWFufSBXaGV0aGVyIHRoZSB2YWx1ZSBpcyBhbiBlbXB0eSBzdHJpbmdcbiAqL1xuZXhwb3J0IGNvbnN0IGlzRW1wdHlTdHJpbmcgPSAodmFsdWU6IHVua25vd24pOiBib29sZWFuID0+IHZhbHVlID09PSAnJztcblxuLyoqXG4gKiBDaGVjayBpZiBhIGNvbXBvbmVudCBoYXMgYW4gYWNjZXNzaWJpbGl0eSBwcm9wXG4gKlxuICogQHBhcmFtIHtvYmplY3R9IHByb3BzXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn0gV2hldGhlciB0aGUgY29tcG9uZW50IGhhcyBhbiBhY2Nlc3NpYmlsaXR5IHByb3BcbiAqL1xuZXhwb3J0IGNvbnN0IGhhc0ExMXlQcm9wID0gKHByb3BzOiBSZWNvcmQ8c3RyaW5nLCBhbnk+KSA9PiB7XG4gIGZvciAoY29uc3QgcHJvcCBpbiBwcm9wcykge1xuICAgIGlmIChwcm9wLnN0YXJ0c1dpdGgoJ2FyaWEtJykgfHwgcHJvcCA9PT0gJ3JvbGUnIHx8IHByb3AgPT09ICd0aXRsZScpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgfVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fcomponents%2Frealtime-assistant.tsx%22%2C%22ids%22%3A%5B%22RealtimeAssistant%22%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fcomponents%2Frealtime-assistant.tsx%22%2C%22ids%22%3A%5B%22RealtimeAssistant%22%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/realtime-assistant.tsx */ \"(app-pages-browser)/./components/realtime-assistant.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZyYXRoaSUyRndvcmslMkZJbnN0cnVtZW50LTIlMkZzeXNfZGVzaWduX2luc3RydW1lbnQlMkZjb21wb25lbnRzJTJGcmVhbHRpbWUtYXNzaXN0YW50LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlJlYWx0aW1lQXNzaXN0YW50JTIyJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ01BQWtLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJSZWFsdGltZUFzc2lzdGFudFwiXSAqLyBcIi9Vc2Vycy9yYXRoaS93b3JrL0luc3RydW1lbnQtMi9zeXNfZGVzaWduX2luc3RydW1lbnQvY29tcG9uZW50cy9yZWFsdGltZS1hc3Npc3RhbnQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fcomponents%2Frealtime-assistant.tsx%22%2C%22ids%22%3A%5B%22RealtimeAssistant%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYXRoaS93b3JrL0luc3RydW1lbnQtMi9zeXNfZGVzaWduX2luc3RydW1lbnQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fcomponents%2Frealtime-assistant.tsx%22%2C%22ids%22%3A%5B%22RealtimeAssistant%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);