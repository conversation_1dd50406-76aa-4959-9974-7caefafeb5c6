/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c9472dcd3df5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYXRoaS93b3JrL0luc3RydW1lbnQtMi9zeXNfZGVzaWduX2luc3RydW1lbnQvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYzk0NzJkY2QzZGY1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"Realtime Interview Assistant\",\n    description: \"Speech-to-speech assistant for mock system design interviews\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased\",\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDdUI7QUFFaEIsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBVTtzQkFDYko7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYXRoaS93b3JrL0luc3RydW1lbnQtMi9zeXNfZGVzaWduX2luc3RydW1lbnQvYXBwL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIlJlYWx0aW1lIEludGVydmlldyBBc3Npc3RhbnRcIixcbiAgZGVzY3JpcHRpb246IFwiU3BlZWNoLXRvLXNwZWVjaCBhc3Npc3RhbnQgZm9yIG1vY2sgc3lzdGVtIGRlc2lnbiBpbnRlcnZpZXdzXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9XCJhbnRpYWxpYXNlZFwiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_realtime_assistant__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/realtime-assistant */ \"(rsc)/./components/realtime-assistant.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_realtime_assistant__WEBPACK_IMPORTED_MODULE_1__.RealtimeAssistant, {}, void 0, false, {\n        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/app/page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBb0U7QUFFckQsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELDZFQUFpQkE7Ozs7O0FBQzNCIiwic291cmNlcyI6WyIvVXNlcnMvcmF0aGkvd29yay9JbnN0cnVtZW50LTIvc3lzX2Rlc2lnbl9pbnN0cnVtZW50L2FwcC9wYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBSZWFsdGltZUFzc2lzdGFudCB9IGZyb20gXCJAL2NvbXBvbmVudHMvcmVhbHRpbWUtYXNzaXN0YW50XCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIHJldHVybiA8UmVhbHRpbWVBc3Npc3RhbnQgLz47XG59XG4iXSwibmFtZXMiOlsiUmVhbHRpbWVBc3Npc3RhbnQiLCJIb21lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/realtime-assistant.tsx":
/*!*******************************************!*\
  !*** ./components/realtime-assistant.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RealtimeAssistant: () => (/* binding */ RealtimeAssistant)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const RealtimeAssistant = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call RealtimeAssistant() from the server but RealtimeAssistant is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx",
"RealtimeAssistant",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"/Users/<USER>/work/Instrument-2/sys_design_instrument/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/work/Instrument-2/sys_design_instrument/app/layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/work/Instrument-2/sys_design_instrument/app/page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fcomponents%2Frealtime-assistant.tsx%22%2C%22ids%22%3A%5B%22RealtimeAssistant%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fcomponents%2Frealtime-assistant.tsx%22%2C%22ids%22%3A%5B%22RealtimeAssistant%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/realtime-assistant.tsx */ \"(rsc)/./components/realtime-assistant.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcmF0aGklMkZ3b3JrJTJGSW5zdHJ1bWVudC0yJTJGc3lzX2Rlc2lnbl9pbnN0cnVtZW50JTJGY29tcG9uZW50cyUyRnJlYWx0aW1lLWFzc2lzdGFudC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJSZWFsdGltZUFzc2lzdGFudCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0xBQWtLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJSZWFsdGltZUFzc2lzdGFudFwiXSAqLyBcIi9Vc2Vycy9yYXRoaS93b3JrL0luc3RydW1lbnQtMi9zeXNfZGVzaWduX2luc3RydW1lbnQvY29tcG9uZW50cy9yZWFsdGltZS1hc3Npc3RhbnQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fcomponents%2Frealtime-assistant.tsx%22%2C%22ids%22%3A%5B%22RealtimeAssistant%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/audio-controls.tsx":
/*!***************************************!*\
  !*** ./components/audio-controls.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioControls: () => (/* binding */ AudioControls)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* __next_internal_client_entry_do_not_use__ AudioControls auto */ \n\nfunction AudioControls({ isMuted, playbackRate, onMuteToggle, onPlaybackRateChange }) {\n    const playbackRates = [\n        0.5,\n        0.75,\n        1.0,\n        1.25,\n        1.5,\n        2.0\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onMuteToggle,\n                className: `p-2 rounded-md transition-colors ${isMuted ? \"bg-red-100 text-red-700 hover:bg-red-200\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"}`,\n                title: isMuted ? \"Unmute (M)\" : \"Mute (M)\",\n                children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    size: 20\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 20\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    size: 20\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 44\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: \"Speed:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: playbackRate,\n                        onChange: (e)=>onPlaybackRateChange(parseFloat(e.target.value)),\n                        className: \"px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                        children: playbackRates.map((rate)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: rate,\n                                children: [\n                                    rate,\n                                    \"x\"\n                                ]\n                            }, rate, true, {\n                                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-500\",\n                children: isMuted ? \"Audio muted\" : `Playing at ${playbackRate}x speed`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/audio-controls.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/audio-controls.tsx\n");

/***/ }),

/***/ "(ssr)/./components/connection-status.tsx":
/*!******************************************!*\
  !*** ./components/connection-status.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionStatus: () => (/* binding */ ConnectionStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ ConnectionStatus auto */ \nfunction ConnectionStatus({ isConnected, isConnecting, onConnect, onDisconnect }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-3 h-3 rounded-full ${isConnected ? \"bg-green-500\" : isConnecting ? \"bg-yellow-500 animate-pulse\" : \"bg-red-500\"}`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/connection-status.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: isConnected ? \"Connected\" : isConnecting ? \"Connecting...\" : \"Disconnected\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/connection-status.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/connection-status.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: isConnected ? onDisconnect : onConnect,\n                disabled: isConnecting,\n                className: `px-4 py-2 rounded-md text-sm font-medium transition-colors ${isConnected ? \"bg-red-100 text-red-700 hover:bg-red-200\" : \"bg-blue-100 text-blue-700 hover:bg-blue-200\"} disabled:opacity-50 disabled:cursor-not-allowed`,\n                children: isConnecting ? \"Connecting...\" : isConnected ? \"Disconnect\" : \"Connect\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/connection-status.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/connection-status.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/connection-status.tsx\n");

/***/ }),

/***/ "(ssr)/./components/realtime-assistant.tsx":
/*!*******************************************!*\
  !*** ./components/realtime-assistant.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeAssistant: () => (/* binding */ RealtimeAssistant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_realtime_websocket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/realtime-websocket */ \"(ssr)/./lib/realtime-websocket.ts\");\n/* harmony import */ var _lib_audio_pipeline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/audio-pipeline */ \"(ssr)/./lib/audio-pipeline.ts\");\n/* harmony import */ var _lib_realtime_runtime_adapter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/realtime-runtime-adapter */ \"(ssr)/./lib/realtime-runtime-adapter.ts\");\n/* harmony import */ var _audio_controls__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./audio-controls */ \"(ssr)/./components/audio-controls.tsx\");\n/* harmony import */ var _connection_status__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./connection-status */ \"(ssr)/./components/connection-status.tsx\");\n/* harmony import */ var _trigger_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./trigger-button */ \"(ssr)/./components/trigger-button.tsx\");\n/* harmony import */ var _simple_thread__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./simple-thread */ \"(ssr)/./components/simple-thread.tsx\");\n/* __next_internal_client_entry_do_not_use__ RealtimeAssistant auto */ \n\n\n\n\n\n\n\n\n// Interview-specific system prompt\nconst INTERVIEW_SYSTEM_PROMPT = `You are an expert system design interviewer. Listen to the conversation between the interviewee and interviewer but only respond with questions, clarifications, or feedback when the interviewee says \"AI, your turn\" or similar trigger phrases. \n\nMaintain context across exchanges and provide insightful questions that help evaluate the candidate's system design thinking. Focus on:\n- Scalability considerations\n- Trade-offs and design decisions\n- Clarifying requirements\n- Probing deeper into architectural choices\n- Identifying potential bottlenecks\n\nKeep responses concise and interview-appropriate.`;\nfunction RealtimeAssistant() {\n    // Connection state\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Audio state\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [playbackRate, setPlaybackRate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    // Refs\n    const wsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioPipelineRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const runtimeAdapterRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize runtime adapter\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RealtimeAssistant.useEffect\": ()=>{\n            runtimeAdapterRef.current = new _lib_realtime_runtime_adapter__WEBPACK_IMPORTED_MODULE_4__.RealtimeRuntimeAdapter(wsRef);\n        }\n    }[\"RealtimeAssistant.useEffect\"], []);\n    // Initialize audio pipeline\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RealtimeAssistant.useEffect\": ()=>{\n            const initAudio = {\n                \"RealtimeAssistant.useEffect.initAudio\": async ()=>{\n                    try {\n                        const pipeline = new _lib_audio_pipeline__WEBPACK_IMPORTED_MODULE_3__.AudioPipeline({\n                            sampleRate: 24000,\n                            channels: 1,\n                            echoCancellation: true\n                        });\n                        pipeline.setOnError({\n                            \"RealtimeAssistant.useEffect.initAudio\": (error)=>{\n                                setError(`Audio error: ${error.message}`);\n                            }\n                        }[\"RealtimeAssistant.useEffect.initAudio\"]);\n                        await pipeline.initialize();\n                        audioPipelineRef.current = pipeline;\n                    } catch (error) {\n                        setError(`Failed to initialize audio: ${error.message}`);\n                    }\n                }\n            }[\"RealtimeAssistant.useEffect.initAudio\"];\n            initAudio();\n            return ({\n                \"RealtimeAssistant.useEffect\": ()=>{\n                    audioPipelineRef.current?.destroy();\n                }\n            })[\"RealtimeAssistant.useEffect\"];\n        }\n    }[\"RealtimeAssistant.useEffect\"], []);\n    // WebSocket event handler\n    const handleRealtimeEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RealtimeAssistant.useCallback[handleRealtimeEvent]\": (event)=>{\n            console.log(\"Realtime event:\", event);\n            // Forward event to runtime adapter\n            runtimeAdapterRef.current?.handleRealtimeEvent(event);\n            switch(event.type){\n                case \"session.created\":\n                    console.log(\"Session created\");\n                    break;\n                case \"input_audio_buffer.speech_started\":\n                    console.log(\"Speech started\");\n                    break;\n                case \"input_audio_buffer.speech_stopped\":\n                    console.log(\"Speech stopped\");\n                    break;\n                case \"response.audio.delta\":\n                    // Play audio delta\n                    if (event.delta && audioPipelineRef.current) {\n                        audioPipelineRef.current.playAudioDelta(event.delta);\n                    }\n                    break;\n                case \"error\":\n                    setError(`Realtime API error: ${event.error?.message || \"Unknown error\"}`);\n                    break;\n                default:\n                    break;\n            }\n        }\n    }[\"RealtimeAssistant.useCallback[handleRealtimeEvent]\"], []);\n    // Connect to realtime API\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RealtimeAssistant.useCallback[connect]\": async ()=>{\n            if (isConnecting || isConnected) return;\n            setIsConnecting(true);\n            setError(null);\n            try {\n                const ws = new _lib_realtime_websocket__WEBPACK_IMPORTED_MODULE_2__.RealtimeWebSocket({\n                    apiKey: \"\",\n                    model: \"gpt-4o-realtime-preview-2024-10-01\",\n                    voice: \"alloy\",\n                    instructions: INTERVIEW_SYSTEM_PROMPT,\n                    onEvent: handleRealtimeEvent,\n                    onError: {\n                        \"RealtimeAssistant.useCallback[connect]\": (error)=>setError(error.message)\n                    }[\"RealtimeAssistant.useCallback[connect]\"],\n                    onConnect: {\n                        \"RealtimeAssistant.useCallback[connect]\": ()=>{\n                            setIsConnected(true);\n                            setIsConnecting(false);\n                            startRecording();\n                        }\n                    }[\"RealtimeAssistant.useCallback[connect]\"],\n                    onDisconnect: {\n                        \"RealtimeAssistant.useCallback[connect]\": ()=>{\n                            setIsConnected(false);\n                            setIsRecording(false);\n                        }\n                    }[\"RealtimeAssistant.useCallback[connect]\"]\n                });\n                await ws.connect();\n                wsRef.current = ws;\n            } catch (error) {\n                setError(error.message);\n                setIsConnecting(false);\n            }\n        }\n    }[\"RealtimeAssistant.useCallback[connect]\"], [\n        isConnecting,\n        isConnected,\n        handleRealtimeEvent\n    ]);\n    // Disconnect\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RealtimeAssistant.useCallback[disconnect]\": ()=>{\n            wsRef.current?.disconnect();\n            wsRef.current = null;\n            setIsConnected(false);\n            setIsRecording(false);\n        }\n    }[\"RealtimeAssistant.useCallback[disconnect]\"], []);\n    // Start continuous recording\n    const startRecording = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RealtimeAssistant.useCallback[startRecording]\": ()=>{\n            if (!audioPipelineRef.current || !wsRef.current || isRecording) return;\n            audioPipelineRef.current.startRecording({\n                \"RealtimeAssistant.useCallback[startRecording]\": (audioData)=>{\n                    wsRef.current?.appendAudioBuffer(audioData);\n                }\n            }[\"RealtimeAssistant.useCallback[startRecording]\"]);\n            setIsRecording(true);\n        }\n    }[\"RealtimeAssistant.useCallback[startRecording]\"], [\n        isRecording\n    ]);\n    // Manual trigger for AI response\n    const triggerResponse = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RealtimeAssistant.useCallback[triggerResponse]\": ()=>{\n            if (!wsRef.current || !isConnected) return;\n            // Commit the audio buffer and request response\n            wsRef.current.commitAudioBuffer();\n            wsRef.current.createResponse();\n        }\n    }[\"RealtimeAssistant.useCallback[triggerResponse]\"], [\n        isConnected\n    ]);\n    // Audio controls\n    const handleMuteToggle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RealtimeAssistant.useCallback[handleMuteToggle]\": ()=>{\n            const newMuted = !isMuted;\n            setIsMuted(newMuted);\n            audioPipelineRef.current?.setMuted(newMuted);\n        }\n    }[\"RealtimeAssistant.useCallback[handleMuteToggle]\"], [\n        isMuted\n    ]);\n    const handlePlaybackRateChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RealtimeAssistant.useCallback[handlePlaybackRateChange]\": (rate)=>{\n            setPlaybackRate(rate);\n            audioPipelineRef.current?.setPlaybackRate(rate);\n        }\n    }[\"RealtimeAssistant.useCallback[handlePlaybackRateChange]\"], []);\n    // Keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RealtimeAssistant.useEffect\": ()=>{\n            const handleKeyPress = {\n                \"RealtimeAssistant.useEffect.handleKeyPress\": (event)=>{\n                    if (event.code === \"Space\" && !event.repeat) {\n                        event.preventDefault();\n                        triggerResponse();\n                    } else if (event.code === \"KeyM\" && !event.repeat) {\n                        event.preventDefault();\n                        handleMuteToggle();\n                    }\n                }\n            }[\"RealtimeAssistant.useEffect.handleKeyPress\"];\n            window.addEventListener(\"keydown\", handleKeyPress);\n            return ({\n                \"RealtimeAssistant.useEffect\": ()=>window.removeEventListener(\"keydown\", handleKeyPress)\n            })[\"RealtimeAssistant.useEffect\"];\n        }\n    }[\"RealtimeAssistant.useEffect\"], [\n        triggerResponse,\n        handleMuteToggle\n    ]);\n    if (!runtimeAdapterRef.current) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n            lineNumber: 203,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white border-b px-6 py-4 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold\",\n                            children: \"Realtime Interview Assistant\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_connection_status__WEBPACK_IMPORTED_MODULE_6__.ConnectionStatus, {\n                            isConnected: isConnected,\n                            isConnecting: isConnecting,\n                            onConnect: connect,\n                            onDisconnect: disconnect\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border-l-4 border-red-400 p-4 mx-6 mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-700\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setError(null),\n                            className: \"text-red-600 hover:text-red-800 text-sm mt-2\",\n                            children: \"Dismiss\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_simple_thread__WEBPACK_IMPORTED_MODULE_8__.SimpleThread, {\n                    runtime: runtimeAdapterRef.current\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white border-t p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_audio_controls__WEBPACK_IMPORTED_MODULE_5__.AudioControls, {\n                                    isMuted: isMuted,\n                                    playbackRate: playbackRate,\n                                    onMuteToggle: handleMuteToggle,\n                                    onPlaybackRateChange: handlePlaybackRateChange\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trigger_button__WEBPACK_IMPORTED_MODULE_7__.TriggerButton, {\n                                    onTrigger: triggerResponse,\n                                    disabled: !isConnected,\n                                    isRecording: isRecording\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 mt-2 text-center\",\n                            children: [\n                                \"Press \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                    className: \"px-1 py-0.5 bg-gray-100 rounded\",\n                                    children: \"Space\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 19\n                                }, this),\n                                \" to trigger AI response •\",\n                                \" \",\n                                \"Press \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                    className: \"px-1 py-0.5 bg-gray-100 rounded\",\n                                    children: \"M\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 19\n                                }, this),\n                                \" to mute/unmute\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n            lineNumber: 209,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/realtime-assistant.tsx\n");

/***/ }),

/***/ "(ssr)/./components/simple-thread.tsx":
/*!**************************************!*\
  !*** ./components/simple-thread.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleThread: () => (/* binding */ SimpleThread)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SimpleThread auto */ \n\nfunction SimpleThread({ runtime }) {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleThread.useEffect\": ()=>{\n            const updateMessages = {\n                \"SimpleThread.useEffect.updateMessages\": ()=>{\n                    setMessages([\n                        ...runtime.messages\n                    ]);\n                }\n            }[\"SimpleThread.useEffect.updateMessages\"];\n            // Subscribe to runtime changes\n            const unsubscribe = runtime.subscribe(updateMessages);\n            // Initial load\n            updateMessages();\n            return unsubscribe;\n        }\n    }[\"SimpleThread.useEffect\"], [\n        runtime\n    ]);\n    if (messages.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center text-gray-500\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium mb-2\",\n                        children: \"Ready to start\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm\",\n                        children: \"Connect and start speaking. Press spacebar to trigger AI responses.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 overflow-y-auto p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4 max-w-4xl mx-auto\",\n            children: [\n                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex ${message.role === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${message.role === \"user\" ? \"bg-blue-600 text-white\" : \"bg-white border border-gray-200 shadow-sm\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm leading-relaxed whitespace-pre-wrap\",\n                                    children: message.content\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `text-xs mt-2 ${message.role === \"user\" ? \"text-blue-100\" : \"text-gray-500\"}`,\n                                    children: [\n                                        new Date(message.timestamp).toLocaleTimeString(),\n                                        message.type === \"audio\" && \" • Audio\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this)\n                    }, message.id, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)),\n                messages.length > 0 && messages[messages.length - 1]?.role === \"assistant\" && messages[messages.length - 1]?.id.startsWith(\"temp-\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-start\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-xs lg:max-w-md px-4 py-3 rounded-lg bg-gray-50 border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: \"0.1s\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: \"0.2s\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"AI is responding...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/simple-thread.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/simple-thread.tsx\n");

/***/ }),

/***/ "(ssr)/./components/trigger-button.tsx":
/*!***************************************!*\
  !*** ./components/trigger-button.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TriggerButton: () => (/* binding */ TriggerButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* __next_internal_client_entry_do_not_use__ TriggerButton auto */ \n\nfunction TriggerButton({ onTrigger, disabled, isRecording }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        className: \"w-4 h-4 text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/trigger-button.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-4 h-4 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/trigger-button.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600\",\n                        children: isRecording ? \"Listening...\" : \"Not recording\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/trigger-button.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/trigger-button.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onTrigger,\n                disabled: disabled,\n                className: `px-6 py-3 rounded-lg font-medium transition-all ${disabled ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 shadow-md hover:shadow-lg\"}`,\n                title: \"Trigger AI Response (Spacebar)\",\n                children: \"\\uD83C\\uDFA4 Ask AI\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/trigger-button.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/trigger-button.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/trigger-button.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/audio-pipeline.ts":
/*!*******************************!*\
  !*** ./lib/audio-pipeline.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioPipeline: () => (/* binding */ AudioPipeline)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ AudioPipeline auto */ class AudioPipeline {\n    constructor(config = {}){\n        this.audioContext = null;\n        this.mediaStream = null;\n        this.mediaRecorder = null;\n        this.gainNode = null;\n        this.isRecording = false;\n        this.isMuted = false;\n        this.playbackRate = 1.0;\n        this.audioQueue = [];\n        this.isPlaying = false;\n        this.config = {\n            sampleRate: 24000,\n            channels: 1,\n            echoCancellation: true,\n            noiseSuppression: true,\n            autoGainControl: true,\n            ...config\n        };\n    }\n    async initialize() {\n        try {\n            // Initialize AudioContext\n            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({\n                sampleRate: this.config.sampleRate\n            });\n            // Create gain node for volume control\n            this.gainNode = this.audioContext.createGain();\n            this.gainNode.connect(this.audioContext.destination);\n            // Get microphone access\n            this.mediaStream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: this.config.sampleRate,\n                    channelCount: this.config.channels,\n                    echoCancellation: this.config.echoCancellation,\n                    noiseSuppression: this.config.noiseSuppression,\n                    autoGainControl: this.config.autoGainControl\n                }\n            });\n            // Setup MediaRecorder for continuous audio capture\n            this.setupMediaRecorder();\n        } catch (error) {\n            this.onError?.(error);\n            throw error;\n        }\n    }\n    setupMediaRecorder() {\n        if (!this.mediaStream) return;\n        // Use webm/opus for better compression and compatibility\n        const mimeType = MediaRecorder.isTypeSupported('audio/webm;codecs=opus') ? 'audio/webm;codecs=opus' : 'audio/webm';\n        this.mediaRecorder = new MediaRecorder(this.mediaStream, {\n            mimeType,\n            audioBitsPerSecond: 64000\n        });\n        this.mediaRecorder.ondataavailable = (event)=>{\n            if (event.data.size > 0) {\n                this.processAudioChunk(event.data);\n            }\n        };\n        this.mediaRecorder.onerror = (event)=>{\n            this.onError?.(new Error('MediaRecorder error'));\n        };\n    }\n    async processAudioChunk(blob) {\n        try {\n            // Convert blob to ArrayBuffer\n            const arrayBuffer = await blob.arrayBuffer();\n            // Convert to PCM 16-bit format expected by OpenAI\n            const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);\n            const pcmData = this.convertToPCM16(audioBuffer);\n            // Convert to base64\n            const base64Audio = this.arrayBufferToBase64(pcmData);\n            // Send to callback\n            this.onAudioData?.(base64Audio);\n        } catch (error) {\n            console.error('Error processing audio chunk:', error);\n        }\n    }\n    convertToPCM16(audioBuffer) {\n        const length = audioBuffer.length;\n        const pcm16 = new Int16Array(length);\n        const channelData = audioBuffer.getChannelData(0); // Use first channel\n        for(let i = 0; i < length; i++){\n            // Convert float32 (-1 to 1) to int16 (-32768 to 32767)\n            const sample = Math.max(-1, Math.min(1, channelData[i]));\n            pcm16[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;\n        }\n        return pcm16.buffer;\n    }\n    arrayBufferToBase64(buffer) {\n        const bytes = new Uint8Array(buffer);\n        let binary = '';\n        for(let i = 0; i < bytes.byteLength; i++){\n            binary += String.fromCharCode(bytes[i]);\n        }\n        return btoa(binary);\n    }\n    // Audio input control\n    startRecording(onAudioData) {\n        if (!this.mediaRecorder || this.isRecording) return;\n        this.onAudioData = onAudioData;\n        this.isRecording = true;\n        // Start recording with small chunks for real-time processing\n        this.mediaRecorder.start(100); // 100ms chunks\n    }\n    stopRecording() {\n        if (!this.mediaRecorder || !this.isRecording) return;\n        this.isRecording = false;\n        this.mediaRecorder.stop();\n    }\n    // Audio output control\n    async playAudioDelta(base64Audio) {\n        if (!this.audioContext || this.isMuted) return;\n        try {\n            // Decode base64 to ArrayBuffer\n            const binaryString = atob(base64Audio);\n            const bytes = new Uint8Array(binaryString.length);\n            for(let i = 0; i < binaryString.length; i++){\n                bytes[i] = binaryString.charCodeAt(i);\n            }\n            // Convert PCM16 to AudioBuffer\n            const audioBuffer = await this.pcm16ToAudioBuffer(bytes.buffer);\n            // Add to queue or play immediately\n            if (this.isPlaying) {\n                this.audioQueue.push(audioBuffer);\n            } else {\n                this.playAudioBuffer(audioBuffer);\n            }\n        } catch (error) {\n            console.error('Error playing audio delta:', error);\n        }\n    }\n    async pcm16ToAudioBuffer(pcmData) {\n        const pcm16 = new Int16Array(pcmData);\n        const audioBuffer = this.audioContext.createBuffer(1, pcm16.length, this.config.sampleRate);\n        const channelData = audioBuffer.getChannelData(0);\n        for(let i = 0; i < pcm16.length; i++){\n            // Convert int16 to float32\n            channelData[i] = pcm16[i] / (pcm16[i] < 0 ? 0x8000 : 0x7FFF);\n        }\n        return audioBuffer;\n    }\n    playAudioBuffer(audioBuffer) {\n        if (!this.audioContext || !this.gainNode) return;\n        const source = this.audioContext.createBufferSource();\n        source.buffer = audioBuffer;\n        source.playbackRate.value = this.playbackRate;\n        source.connect(this.gainNode);\n        this.isPlaying = true;\n        source.onended = ()=>{\n            this.isPlaying = false;\n            // Play next in queue\n            if (this.audioQueue.length > 0) {\n                const nextBuffer = this.audioQueue.shift();\n                this.playAudioBuffer(nextBuffer);\n            }\n        };\n        source.start();\n    }\n    // Audio controls\n    setPlaybackRate(rate) {\n        this.playbackRate = Math.max(0.25, Math.min(4.0, rate));\n    }\n    setMuted(muted) {\n        this.isMuted = muted;\n        if (this.gainNode) {\n            this.gainNode.gain.value = muted ? 0 : 1;\n        }\n    }\n    getMuted() {\n        return this.isMuted;\n    }\n    getPlaybackRate() {\n        return this.playbackRate;\n    }\n    // Cleanup\n    destroy() {\n        this.stopRecording();\n        if (this.mediaStream) {\n            this.mediaStream.getTracks().forEach((track)=>track.stop());\n            this.mediaStream = null;\n        }\n        if (this.audioContext) {\n            this.audioContext.close();\n            this.audioContext = null;\n        }\n        this.audioQueue = [];\n        this.isPlaying = false;\n    }\n    // Event handlers\n    setOnError(callback) {\n        this.onError = callback;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/audio-pipeline.ts\n");

/***/ }),

/***/ "(ssr)/./lib/realtime-runtime-adapter.ts":
/*!*****************************************!*\
  !*** ./lib/realtime-runtime-adapter.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeRuntimeAdapter: () => (/* binding */ RealtimeRuntimeAdapter)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ RealtimeRuntimeAdapter auto */ class RealtimeRuntimeAdapter {\n    constructor(wsRef){\n        this._messages = [];\n        this.subscribers = new Set();\n        this.messageIdCounter = 0;\n        this.currentAssistantMessage = \"\";\n        this.wsRef = wsRef;\n    }\n    // Subscribe to runtime changes\n    subscribe(callback) {\n        this.subscribers.add(callback);\n        return ()=>this.subscribers.delete(callback);\n    }\n    notifySubscribers() {\n        this.subscribers.forEach((callback)=>callback());\n    }\n    // Get current thread messages\n    get messages() {\n        return this._messages;\n    }\n    // Add message to thread\n    addMessage(message) {\n        this._messages.push(message);\n        this.notifySubscribers();\n    }\n    // Update current assistant message (for streaming)\n    updateCurrentAssistantMessage(content) {\n        this.currentAssistantMessage = content;\n        // Find or create current assistant message\n        const lastMessage = this._messages[this._messages.length - 1];\n        if (lastMessage && lastMessage.role === \"assistant\" && lastMessage.id.startsWith(\"temp-\")) {\n            // Update existing temporary message\n            lastMessage.content = content;\n        } else {\n            // Create new temporary message\n            this._messages.push({\n                id: `temp-assistant-${this.messageIdCounter++}`,\n                role: \"assistant\",\n                content,\n                timestamp: Date.now(),\n                type: \"text\"\n            });\n        }\n        this.notifySubscribers();\n    }\n    // Finalize current assistant message\n    finalizeAssistantMessage() {\n        const lastMessage = this._messages[this._messages.length - 1];\n        if (lastMessage && lastMessage.role === \"assistant\" && lastMessage.id.startsWith(\"temp-\")) {\n            // Convert temporary message to permanent\n            lastMessage.id = `assistant-${this.messageIdCounter++}`;\n        }\n        this.currentAssistantMessage = \"\";\n        this.notifySubscribers();\n    }\n    // Handle realtime events\n    handleRealtimeEvent(event) {\n        switch(event.type){\n            case \"conversation.item.input_audio_transcription.completed\":\n                // Add user message\n                this.addMessage({\n                    id: `user-${this.messageIdCounter++}`,\n                    role: \"user\",\n                    content: event.transcript,\n                    timestamp: Date.now(),\n                    type: \"text\"\n                });\n                break;\n            case \"response.audio_transcript.delta\":\n                // Update streaming assistant message\n                this.updateCurrentAssistantMessage(this.currentAssistantMessage + (event.delta || \"\"));\n                break;\n            case \"response.audio_transcript.done\":\n                // Finalize assistant message\n                this.finalizeAssistantMessage();\n                break;\n            case \"response.done\":\n                // Ensure message is finalized\n                this.finalizeAssistantMessage();\n                break;\n        }\n    }\n    // Utility methods\n    cancel() {\n        // Cancel current response\n        this.wsRef.current?.cancelResponse();\n    }\n    get isRunning() {\n        return this.wsRef.current?.getConnectionStatus() || false;\n    }\n    // Clear conversation\n    clearMessages() {\n        this._messages = [];\n        this.currentAssistantMessage = \"\";\n        this.notifySubscribers();\n    }\n    // Get message count\n    get messageCount() {\n        return this._messages.length;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/realtime-runtime-adapter.ts\n");

/***/ }),

/***/ "(ssr)/./lib/realtime-websocket.ts":
/*!***********************************!*\
  !*** ./lib/realtime-websocket.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeWebSocket: () => (/* binding */ RealtimeWebSocket)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ RealtimeWebSocket auto */ class RealtimeWebSocket {\n    constructor(config){\n        this.ws = null;\n        this.isConnected = false;\n        this.sessionStartTime = 0;\n        this.SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes\n        this.timeoutId = null;\n        this.config = config;\n    }\n    async connect() {\n        try {\n            // Get ephemeral key\n            const response = await fetch(\"/api/realtime/ephemeral\", {\n                method: \"POST\"\n            });\n            if (!response.ok) {\n                throw new Error(`Failed to get ephemeral key: ${await response.text()}`);\n            }\n            const { ephemeralKey } = await response.json();\n            // Connect to WebSocket\n            const wsUrl = `wss://api.openai.com/v1/realtime?model=${this.config.model || \"gpt-4o-realtime-preview-2024-10-01\"}`;\n            this.ws = new WebSocket(wsUrl, [\n                \"realtime\",\n                `Bearer.${ephemeralKey}`,\n                \"openai-beta.realtime-v1\"\n            ]);\n            this.ws.onopen = ()=>{\n                this.isConnected = true;\n                this.sessionStartTime = Date.now();\n                this.startSessionTimeout();\n                this.initializeSession();\n                this.config.onConnect?.();\n            };\n            this.ws.onmessage = (event)=>{\n                try {\n                    const data = JSON.parse(event.data);\n                    this.handleEvent(data);\n                } catch (error) {\n                    console.error(\"Failed to parse WebSocket message:\", error);\n                }\n            };\n            this.ws.onclose = ()=>{\n                this.isConnected = false;\n                this.clearSessionTimeout();\n                this.config.onDisconnect?.();\n            };\n            this.ws.onerror = (error)=>{\n                this.config.onError?.(new Error(\"WebSocket error\"));\n            };\n        } catch (error) {\n            this.config.onError?.(error);\n        }\n    }\n    initializeSession() {\n        this.sendEvent({\n            type: \"session.update\",\n            session: {\n                modalities: [\n                    \"text\",\n                    \"audio\"\n                ],\n                voice: this.config.voice || \"alloy\",\n                instructions: this.config.instructions || \"You are a helpful assistant.\",\n                turn_detection: null,\n                input_audio_transcription: {\n                    model: \"whisper-1\"\n                },\n                tools: []\n            }\n        });\n    }\n    handleEvent(event) {\n        this.config.onEvent?.(event);\n    }\n    sendEvent(event) {\n        if (this.ws && this.isConnected) {\n            this.ws.send(JSON.stringify(event));\n        }\n    }\n    // Audio input methods\n    appendAudioBuffer(audioData) {\n        this.sendEvent({\n            type: \"input_audio_buffer.append\",\n            audio: audioData\n        });\n    }\n    commitAudioBuffer() {\n        this.sendEvent({\n            type: \"input_audio_buffer.commit\"\n        });\n    }\n    clearAudioBuffer() {\n        this.sendEvent({\n            type: \"input_audio_buffer.clear\"\n        });\n    }\n    // Response control\n    createResponse() {\n        this.sendEvent({\n            type: \"response.create\"\n        });\n    }\n    cancelResponse() {\n        this.sendEvent({\n            type: \"response.cancel\"\n        });\n    }\n    // Session management\n    startSessionTimeout() {\n        this.timeoutId = setTimeout(()=>{\n            this.reconnect();\n        }, this.SESSION_TIMEOUT);\n    }\n    clearSessionTimeout() {\n        if (this.timeoutId) {\n            clearTimeout(this.timeoutId);\n            this.timeoutId = null;\n        }\n    }\n    async reconnect() {\n        this.disconnect();\n        await new Promise((resolve)=>setTimeout(resolve, 1000)); // Wait 1 second\n        await this.connect();\n    }\n    disconnect() {\n        this.clearSessionTimeout();\n        if (this.ws) {\n            this.ws.close();\n            this.ws = null;\n        }\n        this.isConnected = false;\n    }\n    getConnectionStatus() {\n        return this.isConnected;\n    }\n    getSessionDuration() {\n        return this.sessionStartTime ? Date.now() - this.sessionStartTime : 0;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/realtime-websocket.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fcomponents%2Frealtime-assistant.tsx%22%2C%22ids%22%3A%5B%22RealtimeAssistant%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fcomponents%2Frealtime-assistant.tsx%22%2C%22ids%22%3A%5B%22RealtimeAssistant%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/realtime-assistant.tsx */ \"(ssr)/./components/realtime-assistant.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcmF0aGklMkZ3b3JrJTJGSW5zdHJ1bWVudC0yJTJGc3lzX2Rlc2lnbl9pbnN0cnVtZW50JTJGY29tcG9uZW50cyUyRnJlYWx0aW1lLWFzc2lzdGFudC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJSZWFsdGltZUFzc2lzdGFudCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0xBQWtLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJSZWFsdGltZUFzc2lzdGFudFwiXSAqLyBcIi9Vc2Vycy9yYXRoaS93b3JrL0luc3RydW1lbnQtMi9zeXNfZGVzaWduX2luc3RydW1lbnQvY29tcG9uZW50cy9yZWFsdGltZS1hc3Npc3RhbnQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fcomponents%2Frealtime-assistant.tsx%22%2C%22ids%22%3A%5B%22RealtimeAssistant%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frathi%2Fwork%2FInstrument-2%2Fsys_design_instrument&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();